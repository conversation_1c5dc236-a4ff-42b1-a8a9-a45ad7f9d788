<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rekap_absen_dir" language="groovy" pageWidth="1820" pageHeight="800" orientation="Landscape" columnWidth="1780" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="ce037b1b-0955-4192-ac52-15af604cbe95">
	<property name="ireport.zoom" value="0.5644739300537802"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="IDDIR" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="TANGGAL" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT
    peg.NIP,
    peg.ABSEN,
    dbsdm.getDL($P{TANGGAL}, peg.ABSEN) AS ST,
    IF(peg.ID IS NULL, '   ', CONCAT(
        IF(peg.GELAR_DEPAN = '' OR peg.GELAR_DEPAN IS NULL, '', CONCAT(peg.GELAR_DEPAN, '. ')),
        UPPER(peg.NAMA_LENGKAP),
        IF(peg.GELAR_BELAKANG = '' OR peg.GELAR_BELAKANG IS NULL, '', CONCAT(', ', peg.GELAR_BELAKANG))
    )) AS NAMA_LENGKAP,
    MONTHNAME(STR_TO_DATE($P{TANGGAL}, '%Y-%m')) AS BULAN_REKAP,
    YEAR(STR_TO_DATE($P{TANGGAL}, '%Y-%m')) AS TAHUN_REKAP,
    dir.DIREKTORAT,
    SUM(CASE
            WHEN js.SHIFT = 'C' AND js.STATUS = 1 AND js.REF = 2 THEN 1
            ELSE 0
        END) AS CAP,
    SUM(CASE
            WHEN js.SHIFT = 'C' AND js.STATUS = 1 AND js.REF = 1 THEN 1
            ELSE 0
        END) AS CT,
    SUM(CASE
            WHEN js.SHIFT = 'C' AND js.STATUS = 1 AND js.REF IN (3, 4) THEN 1
            ELSE 0
        END) AS CML_CB,
    COUNT(CASE
        WHEN p.DATANG IS NULL
             AND p.PULANG IS NULL
             AND p.KETERANGAN IS NULL
             AND hariLibur(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), peg.ABSEN) = 'SAKIT'
        THEN 1
        ELSE NULL
    END) AS SAKIT,
    COUNT(CASE
        WHEN p.DATANG IS NULL
             AND p.PULANG IS NULL
             AND p.KETERANGAN IS NULL
             AND hariLibur(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), peg.ABSEN) = 'KERJA'
        THEN 1
        ELSE NULL
    END) AS MANGKIR,
    IFNULL(
        (
            SELECT COUNT(*)
            FROM dbsdm.keterangan_absen ka
            WHERE ka.TANGGAL BETWEEN DATE_FORMAT('2024-06-01', '%Y-%m-01')
                                AND LAST_DAY('2024-06-01')
              AND ka.id_pegawai = peg.ABSEN
              AND ka.status_absen != 0
        ), 0
    ) AS LUPAABSEN,
    COALESCE(SUM(
        CASE
            WHEN hariLibur(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), peg.ABSEN) = 'KERJA' THEN
                FLOOR(
                    TIME_TO_SEC(
                        terlambat(
                            IF($P{TANGGAL} <= '2023-05-04',
                               DATE_ADD(jamMasuk(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), peg.ABSEN), INTERVAL 15 MINUTE),
                               jamMasuk(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), peg.ABSEN)
                            ),
                            p.DATANG
                        )
                    ) / 60
                )
            ELSE
                0
        END
    ), 0) AS TELAT,
    COALESCE(SUM(
        CASE
            WHEN hariLibur(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN) = 'KERJA' THEN
                FLOOR(
                    TIME_TO_SEC(
                        pulangAwal(
                            IF(p.TANGGAL <= '2023-05-04',
                               DATE_SUB(jamPulang(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN), INTERVAL 15 MINUTE),
                               jamPulang(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN)
                            ),
                            p.PULANG
                        )
                    ) / 60
                )
            ELSE
                0
        END
    ), 0) AS PULANG_AWAL,
    (COALESCE(
        (
            SELECT
                SUM(
                    IF(
                        hariLibur(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN) = "KERJA",
                        IF(
                            p.TANGGAL <= "2023-05-04",
                            FLOOR(TIME_TO_SEC(terlambat(DATE_ADD(jamMasuk(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN), INTERVAL 15 MINUTE), p.DATANG)) / 60),
                            FLOOR(TIME_TO_SEC(terlambat(jamMasuk(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN), p.DATANG)) / 60)
                        ),
                        NULL
                    )
                ) AS TELAT
            FROM dbsdm.tanggal t
            LEFT JOIN dbsdm.presensi_new p
                ON CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID) = p.TANGGAL
            WHERE t.ID BETWEEN 1 AND DAY(LAST_DAY(TANGGAL))
            AND p.PIN = peg.ABSEN
        ), 0
    ) + COALESCE(
        (
            SELECT
                SUM(
                    IF(
                        hariLibur(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN) = "KERJA",
                        IF(
                            p.TANGGAL <= "2023-05-04",
                            FLOOR(TIME_TO_SEC(pulangAwal(DATE_SUB(jamPulang(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN), INTERVAL 15 MINUTE), p.PULANG)) / 60),
                            FLOOR(TIME_TO_SEC(pulangAwal(jamPulang(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN), p.PULANG)) / 60)
                        ),
                        NULL
                    )
                ) AS PULANG_AWAL
            FROM dbsdm.tanggal t
            LEFT JOIN dbsdm.presensi_new p
                ON CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID) = p.TANGGAL
            WHERE t.ID BETWEEN 1 AND DAY(LAST_DAY(TANGGAL))
            AND p.PIN = peg.ABSEN
        ), 0
    )) AS TOTALKURANG,
    LEAST(FLOOR((COALESCE(dbsdm.getTerlambat_(peg.ABSEN, $P{TANGGAL}), 0) + COALESCE(dbsdm.getPulcep($P{TANGGAL}, peg.ABSEN), 0)) / 450) * 10, 20) AS PERSENTASE,
    IFNULL(
        (SELECT ROUND((LEAST(FLOOR((COALESCE(dbsdm.getTerlambat_(peg.ABSEN, $P{TANGGAL}), 0) + COALESCE(dbsdm.getPulcep($P{TANGGAL}, peg.ABSEN), 0)) / 450) * 10, 20) * JML_REMUN) / 100)
            FROM pendapatan.tb_slip_remun
            WHERE ABSEN = peg.ABSEN AND DATE_FORMAT(BULAN_TAHUN, '%Y-%m') = DATE_FORMAT($P{TANGGAL}, '%Y-%m')
            LIMIT 1), 0) AS PENGURANGAN,
        -- Total kehadiran kerja yang dihitung dari query pertama
    COUNT(CASE
        WHEN p.DATANG IS NOT NULL
             AND p.PULANG IS NOT NULL
             AND hariLibur(CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID), p.PIN) = 'KERJA'
        THEN 1
        ELSE NULL
    END) AS MASUK,
    CASE
        WHEN peg.STATUS_PEGAWAI = 9 THEN 'PNS'
        WHEN peg.STATUS_PEGAWAI = 10 THEN 'NON PNS TETAP'
        WHEN peg.STATUS_PEGAWAI = 11 THEN 'KONTRAK'
        WHEN peg.STATUS_PEGAWAI = 12 THEN 'CPNS'
        WHEN peg.STATUS_PEGAWAI = 1033 THEN 'PPPK'
        WHEN peg.STATUS_PEGAWAI = 1037 THEN 'CALON NON PNS TETAP'
    END AS STATUSPEGAWAI,
    '' AS KETERANGAN

FROM dbsdm.pegawai1 peg
LEFT JOIN dbsdm.pejabat1 pe ON pe.PEGAWAI = peg.ID AND pe.STATUS = 1
LEFT JOIN dbsdm.jabatan j ON j.ID = pe.JABATAN AND j.STATUS = 1
LEFT JOIN dbsdm.unit u ON u.ID = j.UNIT AND u.STATUS = 1
LEFT JOIN dbsdm.direktorat dir ON u.DIREKTORAT = dir.ID AND dir.STATUS = 1
LEFT JOIN dbsdm.tanggal t ON t.ID BETWEEN 1 AND DAY(LAST_DAY(STR_TO_DATE($P{TANGGAL}, '%Y-%m')))
LEFT JOIN dbsdm.presensi_new p ON CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID) = p.TANGGAL
                                 AND p.PIN = peg.ABSEN
LEFT JOIN dbsdm.jadwal_shift js ON js.ABSEN = peg.ABSEN
                                AND CONCAT(DATE_FORMAT($P{TANGGAL}, '%Y-%m'), '-', t.ID) = js.TANGGAL
LEFT JOIN dbsdm.libur l ON CONCAT(DATE_FORMAT($P{TANGGAL}, "%Y-%m"), "-", t.ID) = l.TANGGAL
WHERE peg.STATUS_AKTIF = 1
  AND peg.STATUS_PEGAWAI IN (9, 10, 11, 12, 1033, 1037)
  AND dir.ID = $P{IDDIR}
GROUP BY peg.ABSEN
ORDER BY j.ID ASC, peg.STATUS_PEGAWAI ASC;]]>
	</queryString>
	<field name="NIP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ABSEN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ST" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NAMA_LENGKAP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="BULAN_REKAP" class="java.lang.String"/>
	<field name="TAHUN_REKAP" class="java.lang.Long"/>
	<field name="DIREKTORAT" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CAP" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CT" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CML_CB" class="java.math.BigDecimal"/>
	<field name="SAKIT" class="java.lang.Long"/>
	<field name="MANGKIR" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="LUPAABSEN" class="java.lang.Long"/>
	<field name="TELAT" class="java.math.BigDecimal"/>
	<field name="PULANG_AWAL" class="java.math.BigDecimal"/>
	<field name="TOTALKURANG" class="java.math.BigDecimal"/>
	<field name="PERSENTASE" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="PENGURANGAN" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="MASUK" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="STATUSPEGAWAI" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KETERANGAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="128" splitType="Stretch">
			<staticText>
				<reportElement x="10" y="30" width="1760" height="20" uuid="a4e448b5-f6b9-4ced-8445-6813b4b89a44"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[RUMAH SAKIT KANKER "DHARMAIS"]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="10" width="1760" height="20" uuid="9d81b568-505b-4a64-886d-f16f7060f7c7"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[DAFTAR HADIR PEGAWAI SEBAGAI DASAR PENERIMAAN REMUNERASI]]></text>
			</staticText>
			<textField>
				<reportElement x="10" y="50" width="1760" height="20" uuid="eb17ccc0-9824-4ae2-abe4-886a9b6b8bad"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BULAN : " + $F{BULAN_REKAP} + " " + $F{TAHUN_REKAP}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="32" y="90" width="1000" height="20" uuid="76ca9914-0859-432e-8535-2c01d0550746"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["DIREKTORAT : " + $F{DIREKTORAT}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="21" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="70" splitType="Stretch">
			<staticText>
				<reportElement x="1260" y="40" width="62" height="30" uuid="6c04e06a-f567-43ec-997b-9f20e24c4311"/>
				<box leftPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[%]]></text>
			</staticText>
			<staticText>
				<reportElement x="811" y="40" width="52" height="30" uuid="114f8cdf-3b18-438a-991d-1a9e46eb820f"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[CML CB]]></text>
			</staticText>
			<staticText>
				<reportElement x="1143" y="40" width="117" height="30" uuid="5cead47d-cf8b-434c-8522-6be839dd7564"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TERLAMBAT + PULANG AWAL]]></text>
			</staticText>
			<staticText>
				<reportElement x="1630" y="10" width="139" height="60" uuid="69bc981a-7fcf-4290-969e-39b63de07062"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[KETERANGAN]]></text>
			</staticText>
			<staticText>
				<reportElement x="707" y="40" width="52" height="30" uuid="bbad42ba-10cd-4546-90d6-b0b0adaeb632"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[CAP]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="1019" y="40" width="62" height="30" uuid="3ca1e35b-823b-4f65-8326-b82c852c6f04"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[LAMBAT DATANG]]></text>
			</staticText>
			<textField>
				<reportElement x="655" y="10" width="809" height="30" uuid="15f2ab95-7a1e-4ed3-bfa5-aa9aa6266794"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BULAN_REKAP} + " " + $F{TAHUN_REKAP}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1464" y="10" width="50" height="60" uuid="49226579-6ce4-4c55-a9c1-1f3f99400b59"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[JML HADIR]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="10" width="52" height="60" uuid="55df7f0b-4a95-4676-bd0b-181e1c43bdeb"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="62" y="10" width="154" height="60" uuid="fcf3ae15-fa58-45f5-a78b-298b4c911a74"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NIP]]></text>
			</staticText>
			<staticText>
				<reportElement x="759" y="40" width="52" height="30" uuid="3ed84a14-82e8-4684-9ab1-09ee8897a2d3"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[CT]]></text>
			</staticText>
			<staticText>
				<reportElement x="863" y="40" width="52" height="30" uuid="06c05b1c-4aa5-453d-94aa-0d58d2df19a8"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[S]]></text>
			</staticText>
			<staticText>
				<reportElement x="1322" y="40" width="142" height="30" uuid="218d556c-6f0f-40cf-9341-89d044ea1605"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[POTONGAN RUPIAH]]></text>
			</staticText>
			<staticText>
				<reportElement x="1514" y="10" width="116" height="60" uuid="08c573c2-dd26-49dc-9600-ff0ec7efc36f"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[STATUS PEGAWAI]]></text>
			</staticText>
			<staticText>
				<reportElement x="282" y="10" width="373" height="60" uuid="b42b97b5-edc4-4eb0-8108-c2bcb32363b7"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NAMA]]></text>
			</staticText>
			<staticText>
				<reportElement x="216" y="10" width="66" height="60" uuid="1b732160-56e9-4b9a-92a8-96ed1a7ff67a"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[ABSEN]]></text>
			</staticText>
			<staticText>
				<reportElement x="915" y="40" width="52" height="30" uuid="d375bd82-defb-4bbd-8c95-788f1c5560ad"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[M]]></text>
			</staticText>
			<staticText>
				<reportElement x="1081" y="40" width="62" height="30" uuid="b60f5566-28c4-4107-881e-20d9295208d6"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[PULANG AWAL]]></text>
			</staticText>
			<staticText>
				<reportElement x="967" y="40" width="52" height="30" uuid="a2784eae-0f52-4f0d-9fc2-ecf95b8a0408"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[LUPA ABSEN]]></text>
			</staticText>
			<staticText>
				<reportElement x="655" y="40" width="52" height="30" uuid="0c602dc5-259f-4242-a41a-1f44fb5821d9"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[ST]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="10" y="0" width="52" height="20" uuid="12b67065-9be5-46dc-900f-b9591805cbf1"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="62" y="0" width="154" height="20" uuid="4d71320f-50ab-492f-9727-18d89a5bd5c9"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NIP}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="216" y="0" width="66" height="20" uuid="e9a20213-b999-4acc-9e0e-72701752e905"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ABSEN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="282" y="0" width="373" height="20" uuid="8a4e6ed9-1a61-47be-8c76-32a614285e7e"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NAMA_LENGKAP}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="655" y="0" width="52" height="20" uuid="9fe7f985-0898-4595-9dd1-d5ee83a069bb"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ST}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="707" y="0" width="52" height="20" uuid="dc4877ff-6eb0-40f3-957b-78e086406e8a"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAP}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="759" y="0" width="52" height="20" uuid="0096bebb-880c-4426-ab8b-72d698b45242"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="811" y="0" width="52" height="20" uuid="023a8502-6991-4fbd-b459-5cc082c9033c"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CML_CB}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="863" y="0" width="52" height="20" uuid="707cd568-44da-4883-bbad-85719f8c692f"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SAKIT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="915" y="0" width="52" height="20" uuid="0cac620d-3cbc-44b1-8ed9-89c14a0584f3"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MANGKIR}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="967" y="0" width="52" height="20" uuid="b28b0460-5d4e-4d7e-8e97-d04becd35172"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LUPAABSEN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1019" y="0" width="62" height="20" uuid="20c924ff-0af7-4eb3-a277-10ddaa85a7f3"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TELAT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1081" y="0" width="62" height="20" uuid="09f5b7cb-c19a-487a-9056-0f89be1cf90e"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PULANG_AWAL}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1143" y="0" width="117" height="20" uuid="5c9934f4-0539-489d-9774-63dfeaf2f063"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOTALKURANG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1260" y="0" width="62" height="20" uuid="ba62793e-fa46-48ea-8d88-0e4898fae74e"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PERSENTASE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1464" y="0" width="50" height="20" uuid="add69f35-56e8-40dc-bb46-9dcc0e85b7f1"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MASUK}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1322" y="0" width="142" height="20" uuid="39d961e8-3da0-4caf-9dac-16bd38a929ad"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PENGURANGAN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1514" y="0" width="116" height="20" uuid="82a5dd1c-846c-4c7b-8247-b7396e3cfe8b"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{STATUSPEGAWAI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="1630" y="0" width="139" height="20" uuid="acb8750f-a59a-48d1-aa11-243f75ca5f72"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</detail>
	<columnFooter>
		<band height="45" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="54" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
