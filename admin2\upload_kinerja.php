<?php
//koneksi ke database, username,password  dan namadatabase menyesuaikan 
// mysql_connect('localhost', 'username', 'password');
// mysql_select_db('namadatabase');
  // $serverName = "*************"; 
  //               $options = array(  "UID" => "panca",  "PWD" => "pocs5",  "Database" => "dbsdm");
  //               $conn = sqlsrv_connect($serverName, $options);

    $host = "localhost";
    $user = "root";
    $pass = "";
    $dbname = "dbsdm";

    $conn = new mysqli($host, $user, $pass,$dbname); //koneksi ke mysql
    if ($conn->connect_error) {//Output bila koneksi erorr
        die('Error : ('. $conn->connect_error .') '. $conn->connect_error);
    }

//memanggil file excel_reader
require "excel_reader.php";
 
//jika tombol import ditekan
if(isset($_POST['submit'])){
 
    $target = basename($_FILES['filepegawaiall']['name']) ;
    move_uploaded_file($_FILES['filepegawaiall']['tmp_name'], $target);
 
// tambahkan baris berikut untuk mencegah error is not readable
    chmod($_FILES['filepegawaiall']['name'],0777);
    
    $data = new Spreadsheet_Excel_Reader($_FILES['filepegawaiall']['name'],false);
    
//    menghitung jumlah baris file xls
    $baris = $data->rowcount($sheet_index=0);
    
//    jika kosongkan data dicentang jalankan kode berikut
    $drop = isset( $_POST["drop"] ) ? $_POST["drop"] : 0 ;
    if($drop == 1){
//             kosongkan tabel pegawai
             $truncate ="TRUNCATE TABLE pegawai";
            // mysql_query($truncate);
    };
    $waktu =date('Y-m-d H:i:s');
	$status='1';
	$oleh='55555';$a="select top 1 id from kuantitas order by id desc";
			$b=mysqli_query($conn,$a);
			$c=mysqli_fetch_array($b);
			$id2=$c[0]+1;
//    import data excel mulai baris ke-2 (karena tabel xls ada header pada baris 1)
    for ($i=2; $i<=$baris; $i++)
    {
	
		
    
	  $tsql2= "INSERT INTO kuantitas (ID,PEGAWAI,INDIKATOR,DO,TARGET,BOBOT,STATUS,OLEH,TANGGAL) 
                VALUES ($id2,$data->val($i,1), $data->val($i,2),$data->val($i,3),$data->val($i,4),$data->val($i,5),$status,$oleh,$waktu)";
     				// $var2=array($id2,$data->val($i,1), $data->val($i,2),$data->val($i,3),$data->val($i,4),$data->val($i,5),$status,$oleh,$waktu);
					 if (!mysqli_query($conn, $tsql2)) {
					 die( print_r('gagal'));
        			}
				$hasil=$i;
				$id2++;
			
    }
    
    if(!$hasil){
//          jika import gagal
          // die(mysqli_error());
         die( print_r('gagal'));
      }else{
//          jika impor berhasil
          echo "Data berhasil diimpor.";
    }
    
//    hapus file xls yang udah dibaca
    unlink($_FILES['filepegawaiall']['name']);
}
 
?>
 
<form name="myForm" id="myForm" onSubmit="return validateForm()" action="upload_kinerja.php" method="post" enctype="multipart/form-data">
    <input type="file" id="filepegawaiall" name="filepegawaiall" />
    <input type="submit" name="submit" value="Import" /><br/>
   
</form>
 
<script type="text/javascript">
//    validasi form (hanya file .xls yang diijinkan)
    function validateForm()
    {
        function hasExtension(inputID, exts) {
            var fileName = document.getElementById(inputID).value;
            return (new RegExp('(' + exts.join('|').replace(/\./g, '\\.') + ')$')).test(fileName);
        }
 
        if(!hasExtension('filepegawaiall', ['.xls'])){
            alert("Hanya file XLS (Excel 2003) yang diijinkan.");
            return false;
        }
    }
</script>