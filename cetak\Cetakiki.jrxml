<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PengkajianRJKeperawatanDewasa" language="groovy" pageWidth="1489" pageHeight="1094" columnWidth="1489" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="38b790aa-9be0-40e3-9c3b-1200c79a638d">
	<property name="ireport.zoom" value="0.75"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.callouts" value="##Wed May 16 17:50:22 ICT 2018"/>
	<parameter name="TAHUN" class="java.lang.String"/>
	<parameter name="PEGAWAI" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT a.UNIT, a.NIP, a.NAMA_A, a.JABATAN, a.NIP_ATL, a.NAMA_ATL, a.JABATAN_ATL, a.IKI

	, a.INDIKATOR, a.DO, a.KET

	, a.KET1, a.KET2, MAX(a.TAHUN_ANGKA) PERIODE



	, SUM(a.TARGET) TOTAL_TARGET

	, SUM(a.CAPAIAN) TOTAL_CAPAIAN

	, a.BOBOT BOBOT

	, SUM(a.NILAI) TOTAL_NILAI

	, SUM(IF(a.PERIODE_ANGKA=1,a.CAPAIAN,0)) CAPAIAN_JANUARI

	, SUM(IF(a.PERIODE_ANGKA=1,a.NILAI,0)) NILAI_JANUARI



	, SUM(IF(a.PERIODE_ANGKA=2,a.CAPAIAN,0)) CAPAIAN_FEB

	, SUM(IF(a.PERIODE_ANGKA=2,a.NILAI,0)) NILAI_FEB



	, SUM(IF(a.PERIODE_ANGKA=3,a.CAPAIAN,0)) CAPAIAN_MARET

	, SUM(IF(a.PERIODE_ANGKA=3,a.NILAI,0)) NILAI_MARET



	, SUM(IF(a.PERIODE_ANGKA=4,a.CAPAIAN,0)) CAPAIAN_APRIL

	, SUM(IF(a.PERIODE_ANGKA=4,a.NILAI,0)) NILAI_APRIL



	, SUM(IF(a.PERIODE_ANGKA=5,a.CAPAIAN,0)) CAPAIAN_MAY

	, SUM(IF(a.PERIODE_ANGKA=5,a.NILAI,0)) NILAI_MAY



	, SUM(IF(a.PERIODE_ANGKA=6,a.CAPAIAN,0)) CAPAIAN_JUNE

	, SUM(IF(a.PERIODE_ANGKA=6,a.NILAI,0)) NILAI_JUNE



	, SUM(IF(a.PERIODE_ANGKA=7,a.CAPAIAN,0)) CAPAIAN_JULY

	, SUM(IF(a.PERIODE_ANGKA=7,a.NILAI,0)) NILAI_JULY



	, SUM(IF(a.PERIODE_ANGKA=8,a.CAPAIAN,0)) CAPAIAN_AUG

	, SUM(IF(a.PERIODE_ANGKA=8,a.NILAI,0)) NILAI_AUG



	, SUM(IF(a.PERIODE_ANGKA=9,a.CAPAIAN,0)) CAPAIAN_SEPT

	, SUM(IF(a.PERIODE_ANGKA=9,a.NILAI,0)) NILAI_SEPT



	, SUM(IF(a.PERIODE_ANGKA=10,a.CAPAIAN,0)) CAPAIAN_OCT

	, SUM(IF(a.PERIODE_ANGKA=10,a.NILAI,0)) NILAI_OCT



	, SUM(IF(a.PERIODE_ANGKA=11,a.CAPAIAN,0)) CAPAIAN_NOV

	, SUM(IF(a.PERIODE_ANGKA=11,a.NILAI,0)) NILAI_NOV



	, SUM(IF(a.PERIODE_ANGKA=12 AND a.TAHUN_ANGKA=$P{TAHUN}, a.CAPAIAN,0)) CAPAIAN_DES

	, SUM(IF(a.PERIODE_ANGKA=12 AND a.TAHUN_ANGKA=$P{TAHUN},a.NILAI,0)) NILAI_DES



FROM



(SELECT u.UNIT,p.NIP,

	dbsdm.get_namaLengkap(p.ID) NAMA_A,

	j.JABATAN,

	p1.NIP NIP_ATL,

	dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,

	j1.JABATAN JABATAN_ATL,

	p2.NIP NIP_AT,

	dbsdm.get_namaLengkap(p2.ID) NAMA_AT,

	j2.JABATAN JABATAN_AT, i.IKI,

	kuan.INDIKATOR, kuan.DO DO, kuan.TARGET, kuai.CAPAIAN, ROUND(kuan.BOBOT,3) BOBOT

	, ROUND((kuai.CAPAIAN/kuan.TARGET)*kuan.BOBOT,3) NILAI, 'A' KET, 'KUANTITAS' KET1, 'A1' KET2



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL')))))))))))) PERIODE



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',12,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',1,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',2,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',3,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',4,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',5,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',6,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',7,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',8,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',9,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',10,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',11,'GAGAL')))))))))))) PERIODE_ANGKA



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))TAHUN_ANGKA



FROM pegawai1 p



	LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')

	LEFT JOIN jabatan j ON pj.JABATAN = j.ID

	LEFT JOIN jabatan j1 ON j.PARENT = j1.ID

	LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID

	LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')

	LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')

	LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID

	LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID

	LEFT JOIN unit u ON j.UNIT = u.ID

	LEFT JOIN iki i ON p.ID = i.DINILAI

	LEFT JOIN kuantitasiki kuai ON i.ID = kuai.IKI

	LEFT JOIN kuantitas kuan ON kuai.KUANTITAS = kuan.ID



WHERE p.ID = $P{PEGAWAI} #AND i.ID LIKE '2019%'



UNION



SELECT u.UNIT,p.NIP,

	dbsdm.get_namaLengkap(p.ID) NAMA_A,

	j.JABATAN,

	p1.NIP NIP_ATL,

	dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,

	j1.JABATAN JABATAN_ATL,

	p2.NIP NIP_AT,

	dbsdm.get_namaLengkap(p2.ID) NAMA_AT,

	j2.JABATAN JABATAN_AT, i.IKI,

	kual.INDIKATOR, kual.DO DO, kual.TARGET, kuali.CAPAIAN, ROUND(kual.BOBOT,3) BOBOT

	, ROUND((kuali.CAPAIAN/kual.TARGET)*kual.BOBOT,3) NILAI, 'B' KET, 'KUALITAS' KET1, 'A1' KET2



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',12,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',1,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',2,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',3,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',4,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',5,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',6,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',7,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',8,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',9,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',10,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',11,'GAGAL')))))))))))) PERIODE_ANGKA



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))TAHUN_ANGKA





FROM pegawai1 p



	LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')

	LEFT JOIN jabatan j ON pj.JABATAN = j.ID

	LEFT JOIN jabatan j1 ON j.PARENT = j1.ID

	LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID

	LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')

	LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')

	LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID

	LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID

	LEFT JOIN unit u ON j.UNIT = u.ID

	LEFT JOIN iki i ON p.ID = i.DINILAI

	LEFT JOIN kualitasiki kuali ON i.ID = kuali.IKI

	LEFT JOIN kualitas kual ON kuali.KUALITAS = kual.ID

	WHERE p.ID = $P{PEGAWAI} #AND i.ID LIKE '2019%'



UNION



SELECT u.UNIT,p.NIP,

	dbsdm.get_namaLengkap(p.ID) NAMA_A,

	j.JABATAN,

	p1.NIP NIP_ATL,

	dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,

	j1.JABATAN JABATAN_ATL,

	p2.NIP NIP_AT,

	dbsdm.get_namaLengkap(p2.ID) NAMA_AT,

	j2.JABATAN JABATAN_AT, i.IKI,

	per.INDIKATOR, per.DO DO, per.TARGET, peri.CAPAIAN, ROUND(per.BOBOT,3) BOBOT

	, ROUND((peri.CAPAIAN/per.TARGET)*per.BOBOT,3) NILAI, 'C' KET, 'PERILAKU' KET1, 'A2' KET2



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',12,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',1,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',2,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',3,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',4,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',5,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',6,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',7,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',8,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',9,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',10,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',11,'GAGAL')))))))))))) PERIODE_ANGKA



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))TAHUN_ANGKA







FROM pegawai1 p

	LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')

	LEFT JOIN jabatan j ON pj.JABATAN = j.ID

	LEFT JOIN jabatan j1 ON j.PARENT = j1.ID

	LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID

	LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')

	LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')

	LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID

	LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID

	LEFT JOIN unit u ON j.UNIT = u.ID

	LEFT JOIN iki i ON p.ID = i.DINILAI

	LEFT JOIN perilakuiki peri ON i.ID = peri.IKI

	LEFT JOIN perilaku per ON peri.PERILAKU = per.ID

WHERE p.ID = $P{PEGAWAI} #AND i.ID LIKE '2019%'



UNION



SELECT u.UNIT,p.NIP,

	dbsdm.get_namaLengkap(p.ID) NAMA_A,

	j.JABATAN,

	p1.NIP NIP_ATL,

	dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,

	j1.JABATAN JABATAN_ATL,

	p2.NIP NIP_AT,

	dbsdm.get_namaLengkap(p2.ID) NAMA_AT,

	j2.JABATAN JABATAN_AT, i.IKI,

	kt.INDIKATOR, kt.RINCIAN DO, kt.TARGET, kti.CAPAIAN, ROUND(kti.BOBOT,3) BOBOT

	, ROUND((kti.CAPAIAN/kt.TARGET)*kti.BOBOT,3) NILAI, 'D' KET, 'PENGEMBANGAN PROFESI' KET1, 'A2' KET2



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',12,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',1,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',2,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',3,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',4,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',5,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',6,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',7,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',8,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',9,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',10,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',11,'GAGAL')))))))))))) PERIODE_ANGKA



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))TAHUN_ANGKA







FROM pegawai1 p

	LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')

	LEFT JOIN jabatan j ON pj.JABATAN = j.ID

	LEFT JOIN jabatan j1 ON j.PARENT = j1.ID

	LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID

	LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')

	LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')

	LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID

	LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID

	LEFT JOIN unit u ON j.UNIT = u.ID

	LEFT JOIN iki i ON p.ID = i.DINILAI

	LEFT JOIN kegiatan_tambahaniki kti ON i.ID = kti.IKI

	LEFT JOIN kegiatan_tambahan kt ON kti.KD_TAMBAHAN = kt.ID

WHERE p.ID =$P{PEGAWAI}  #AND i.ID LIKE '2019%'

) a



WHERE a.TAHUN_ANGKA=$P{TAHUN}



GROUP BY a.NIP]]>
	</queryString>
	<field name="UNIT" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NIP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NAMA_A" class="java.lang.String"/>
	<field name="JABATAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NIP_ATL" class="java.lang.String"/>
	<field name="NAMA_ATL" class="java.lang.String"/>
	<field name="JABATAN_ATL" class="java.lang.String"/>
	<field name="IKI" class="java.lang.Double">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="INDIKATOR" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="DO" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KET" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KET1" class="java.lang.String"/>
	<field name="KET2" class="java.lang.String"/>
	<field name="PERIODE" class="java.lang.String">
		<fieldDescription><![CDATA[periode jangka waktunya]]></fieldDescription>
	</field>
	<field name="TOTAL_TARGET" class="java.math.BigDecimal"/>
	<field name="TOTAL_CAPAIAN" class="java.lang.Double"/>
	<field name="BOBOT" class="java.lang.Double">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TOTAL_NILAI" class="java.lang.Double"/>
	<field name="CAPAIAN_JANUARI" class="java.lang.Double"/>
	<field name="NILAI_JANUARI" class="java.lang.Double"/>
	<field name="CAPAIAN_FEB" class="java.lang.Double"/>
	<field name="NILAI_FEB" class="java.lang.Double"/>
	<field name="CAPAIAN_MARET" class="java.lang.Double"/>
	<field name="NILAI_MARET" class="java.lang.Double"/>
	<field name="CAPAIAN_APRIL" class="java.lang.Double"/>
	<field name="NILAI_APRIL" class="java.lang.Double"/>
	<field name="CAPAIAN_MAY" class="java.lang.Double"/>
	<field name="NILAI_MAY" class="java.lang.Double"/>
	<field name="CAPAIAN_JUNE" class="java.lang.Double"/>
	<field name="NILAI_JUNE" class="java.lang.Double"/>
	<field name="CAPAIAN_JULY" class="java.lang.Double"/>
	<field name="NILAI_JULY" class="java.lang.Double"/>
	<field name="CAPAIAN_AUG" class="java.lang.Double"/>
	<field name="NILAI_AUG" class="java.lang.Double"/>
	<field name="CAPAIAN_SEPT" class="java.lang.Double"/>
	<field name="NILAI_SEPT" class="java.lang.Double"/>
	<field name="CAPAIAN_OCT" class="java.lang.Double"/>
	<field name="NILAI_OCT" class="java.lang.Double"/>
	<field name="CAPAIAN_NOV" class="java.lang.Double"/>
	<field name="NILAI_NOV" class="java.lang.Double"/>
	<field name="CAPAIAN_DES" class="java.lang.Double"/>
	<field name="NILAI_DES" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="50" splitType="Stretch">
			<subreport>
				<reportElement x="0" y="0" width="1489" height="50" uuid="fb1b5f99-5717-4b74-a15e-c599a45f8674"/>
				<subreportParameter name="PEGAWAI">
					<subreportParameterExpression><![CDATA[$P{PEGAWAI}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TAHUN">
					<subreportParameterExpression><![CDATA[$P{TAHUN}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\LaporanIKIPerorang.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<break>
				<reportElement x="0" y="21" width="595" height="1" uuid="fe6c2a1e-5f14-4e4b-b72f-9f194f053037"/>
			</break>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="2383" height="50" uuid="1dd829e5-ec73-467f-8ed0-9d90cc21737a"/>
				<subreportParameter name="PEGAWAI">
					<subreportParameterExpression><![CDATA[$P{PEGAWAI}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TAHUN">
					<subreportParameterExpression><![CDATA[$P{TAHUN}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\LaporanIKITahunanPerorangNew.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
