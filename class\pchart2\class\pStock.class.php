<?php
 /*
     pStock - class to draw stock charts

     Version     : 2.1.1
     Made by     : <PERSON><PERSON><PERSON>TI
     Last Update : 28/03/11

     This file can be distributed under the license you can find at :

                       http://www.pchart.net/license

     You can find the whole class documentation on the pChart web site.
 */

 define("STOCK_MISSING_SERIE"	, 180001);

 /* pStock class definition */
 class pStock
  {
   var $pChartObject;
   var $pDataObject;

   /* Class creator */
   function pStock($pChartObject,$pDataObject)
    {
     $this->pChartObject = $pChartObject;
     $this->pDataObject  = $pDataObject;
    }

   /* Draw a stock chart */
   function drawStockChart($Format="")
    {
     $SerieOpen		= isset($Format["SerieOpen"]) ? $Format["SerieOpen"] : "Open";
     $SerieClose	= isset($Format["SerieClose"]) ? $Format["SerieClose"] : "Close";
     $SerieMin		= isset($Format["SerieMin"]) ? $Format["SerieMin"] : "Min";
     $SerieMax		= isset($Format["SerieMax"]) ? $Format["SerieMax"] : "Max";
     $LineWidth		= isset($Format["LineWidth"]) ? $Format["LineWidth"] : 1;
     $LineR		= isset($Format["LineR"]) ? $Format["LineR"] : 0;
     $LineG		= isset($Format["LineG"]) ? $Format["LineG"] : 0;
     $LineB		= isset($Format["LineB"]) ? $Format["LineB"] : 0;
     $LineAlpha		= isset($Format["LineAlpha"]) ? $Format["LineAlpha"] : 100;
     $ExtremityWidth	= isset($Format["ExtremityWidth"]) ? $Format["ExtremityWidth"] : 1;
     $ExtremityLength	= isset($Format["ExtremityLength"]) ? $Format["ExtremityLength"] : 3;
     $ExtremityR	= isset($Format["ExtremityR"]) ? $Format["ExtremityR"] : 0;
     $ExtremityG	= isset($Format["ExtremityG"]) ? $Format["ExtremityG"] : 0;
     $ExtremityB	= isset($Format["ExtremityB"]) ? $Format["ExtremityB"] : 0;
     $ExtremityAlpha	= isset($Format["ExtremityAlpha"]) ? $Format["ExtremityAlpha"] : 100;
     $BoxWidth		= isset($Format["BoxWidth"]) ? $Format["BoxWidth"] : 8;
     $BoxUpR		= isset($Format["BoxUpR"]) ? $Format["BoxUpR"] : 188;
     $BoxUpG		= isset($Format["BoxUpG"]) ? $Format["BoxUpG"] : 224;
     $BoxUpB		= isset($Format["BoxUpB"]) ? $Format["BoxUpB"] : 46;
     $BoxUpAlpha	= isset($Format["BoxUpAlpha"]) ? $Format["BoxUpAlpha"] : 100;
     $BoxUpSurrounding	= isset($Format["BoxUpSurrounding"]) ? $Format["BoxUpSurrounding"] : NULL;
     $BoxUpBorderR	= isset($Format["BoxUpBorderR"]) ? $Format["BoxUpBorderR"] : $BoxUpR-20;
     $BoxUpBorderG	= isset($Format["BoxUpBorderG"]) ? $Format["BoxUpBorderG"] : $BoxUpG-20;
     $BoxUpBorderB	= isset($Format["BoxUpBorderB"]) ? $Format["BoxUpBorderB"] : $BoxUpB-20;
     $BoxUpBorderAlpha	= isset($Format["BoxUpBorderAlpha"]) ? $Format["BoxUpBorderAlpha"] : 100;
     $BoxDownR		= isset($Format["BoxDownR"]) ? $Format["BoxDownR"] : 224;
     $BoxDownG		= isset($Format["BoxDownG"]) ? $Format["BoxDownG"] : 100;
     $BoxDownB		= isset($Format["BoxDownB"]) ? $Format["BoxDownB"] : 46;
     $BoxDownAlpha	= isset($Format["BoxDownAlpha"]) ? $Format["BoxDownAlpha"] : 100;
     $BoxDownSurrounding= isset($Format["BoxDownSurrounding"]) ? $Format["BoxDownSurrounding"] : NULL;
     $BoxDownBorderR	= isset($Format["BoxDownBorderR"]) ? $Format["BoxDownBorderR"] : $BoxDownR-20;
     $BoxDownBorderG	= isset($Format["BoxDownBorderG"]) ? $Format["BoxDownBorderG"] : $BoxDownG-20;
     $BoxDownBorderB	= isset($Format["BoxDownBorderB"]) ? $Format["BoxDownBorderB"] : $BoxDownB-20;
     $BoxDownBorderAlpha= isset($Format["BoxDownBorderAlpha"]) ? $Format["BoxDownBorderAlpha"] : 100;
     $ShadowOnBoxesOnly	= isset($Format["ShadowOnBoxesOnly"]) ? $Format["ShadowOnBoxesOnly"] : TRUE;

     /* Data Processing */
     $Data    = $this->pDataObject->getData();
     $Palette = $this->pDataObject->getPalette();

     if ( $BoxUpSurrounding != NULL )	{ $BoxUpBorderR = $BoxUpR + $BoxUpSurrounding; $BoxUpBorderG = $BoxUpG + $BoxUpSurrounding; $BoxUpBorderB = $BoxUpB + $BoxUpSurrounding; }
     if ( $BoxDownSurrounding != NULL )	{ $BoxDownBorderR = $BoxDownR + $BoxDownSurrounding; $BoxDownBorderG = $BoxDownG + $BoxDownSurrounding; $BoxDownBorderB = $BoxDownB + $BoxDownSurrounding; }

     if ( $LineWidth != 1 ) { $LineOffset = $LineWidth / 2; }
     $BoxOffset = $BoxWidth / 2;

     $Data = $this->pChartObject->DataSet->getData();
     list($XMargin,$XDivs) = $this->pChartObject->scaleGetXSettings();

     if ( !isset($Data["Series"][$SerieOpen]) || !isset($Data["Series"][$SerieClose]) || !isset($Data["Series"][$SerieMin]) || !isset($Data["Series"][$SerieMax]) )
      return(STOCK_MISSING_SERIE);

     $Plots = "";
     foreach($Data["Series"][$SerieOpen]["Data"] as $Key => $Value)
      {
       if ( isset($Data["Series"][$SerieClose]["Data"][$Key]) || isset($Data["Series"][$SerieMin]["Data"][$Key]) || isset($Data["Series"][$SerieMax]["Data"][$Key]) )
        $Plots[] = array($Value,$Data["Series"][$SerieClose]["Data"][$Key],$Data["Series"][$SerieMin]["Data"][$Key],$Data["Series"][$SerieMax]["Data"][$Key]);
      }

     $AxisID	= $Data["Series"][$SerieOpen]["Axis"];
     $Mode	= $Data["Axis"][$AxisID]["Display"];
     $Format	= $Data["Axis"][$AxisID]["Format"];
     $Unit	= $Data["Axis"][$AxisID]["Unit"];

     $YZero	= $this->pChartObject->scaleComputeY(0,array("AxisID"=>$AxisID));
     $XStep	= ($this->pChartObject->GraphAreaX2-$this->pChartObject->GraphAreaX1-$XMargin*2)/$XDivs;

     $X = $this->pChartObject->GraphAreaX1 + $XMargin;
     $Y = $this->pChartObject->GraphAreaY1 + $XMargin;

     $LineSettings	= array("R"=>$LineR,"G"=>$LineG,"B"=>$LineB,"Alpha"=>$LineAlpha);
     $ExtremitySettings	= array("R"=>$ExtremityR,"G"=>$ExtremityG,"B"=>$ExtremityB,"Alpha"=>$ExtremityAlpha);
     $BoxUpSettings	= array("R"=>$BoxUpR,"G"=>$BoxUpG,"B"=>$BoxUpB,"Alpha"=>$BoxUpAlpha,"BorderR"=>$BoxUpBorderR,"BorderG"=>$BoxUpBorderG,"BorderB"=>$BoxUpBorderB,"BorderAlpha"=>$BoxUpBorderAlpha);
     $BoxDownSettings	= array("R"=>$BoxDownR,"G"=>$BoxDownG,"B"=>$BoxDownB,"Alpha"=>$BoxDownAlpha,"BorderR"=>$BoxDownBorderR,"BorderG"=>$BoxDownBorderG,"BorderB"=>$BoxDownBorderB,"BorderAlpha"=>$BoxDownBorderAlpha);

     foreach($Plots as $Key =>$Points)
      {
       $PosArray = $this->pChartObject->scaleComputeY($Points,array("AxisID"=>$AxisID));

       if ( $Data["Orientation"] == SCALE_POS_LEFTRIGHT )
        {
         if ( $YZero > $this->pChartObject->GraphAreaY2-1 ) { $YZero = $this->pChartObject->GraphAreaY2-1; }
         if ( $YZero < $this->pChartObject->GraphAreaY1+1 ) { $YZero = $this->pChartObject->GraphAreaY1+1; }

         if ( $XDivs == 0 ) { $XStep = 0; } else { $XStep = ($this->pChartObject->GraphAreaX2-$this->pChartObject->GraphAreaX1-$XMargin*2)/$XDivs; }

         if ( $ShadowOnBoxesOnly ) { $RestoreShadow = $this->pChartObject->Shadow; $this->pChartObject->Shadow = FALSE; }

         if ( $LineWidth == 1 )
          $this->pChartObject->drawLine($X,$PosArray[2],$X,$PosArray[3],$LineSettings);
         else
          $this->pChartObject->drawFilledRectangle($X-$LineOffset,$PosArray[2],$X+$LineOffset,$PosArray[3],$LineSettings);

         if ( $ExtremityWidth == 1 )
          {
           $this->pChartObject->drawLine($X-$ExtremityLength,$PosArray[2],$X+$ExtremityLength,$PosArray[2],$ExtremitySettings);
           $this->pChartObject->drawLine($X-$ExtremityLength,$PosArray[3],$X+$ExtremityLength,$PosArray[3],$ExtremitySettings);
          }
         else
          {
           $this->pChartObject->drawFilledRectangle($X-$ExtremityLength,$PosArray[2],$X+$ExtremityLength,$PosArray[2]-$ExtremityWidth,$ExtremitySettings);
           $this->pChartObject->drawFilledRectangle($X-$ExtremityLength,$PosArray[3],$X+$ExtremityLength,$PosArray[3]+$ExtremityWidth,$ExtremitySettings);
          }

         if ( $ShadowOnBoxesOnly ) { $this->pChartObject->Shadow = $RestoreShadow; }

         if ( $PosArray[0] > $PosArray[1] )
          $this->pChartObject->drawFilledRectangle($X-$BoxOffset,$PosArray[0],$X+$BoxOffset,$PosArray[1],$BoxUpSettings);
         else
          $this->pChartObject->drawFilledRectangle($X-$BoxOffset,$PosArray[0],$X+$BoxOffset,$PosArray[1],$BoxDownSettings);

         $X = $X + $XStep;
        }
       elseif ( $Data["Orientation"] == SCALE_POS_TOPBOTTOM )
        {
         if ( $YZero > $this->pChartObject->GraphAreaX2-1 ) { $YZero = $this->pChartObject->GraphAreaX2-1; }
         if ( $YZero < $this->pChartObject->GraphAreaX1+1 ) { $YZero = $this->pChartObject->GraphAreaX1+1; }

         if ( $XDivs == 0 ) { $XStep = 0; } else { $XStep = ($this->pChartObject->GraphAreaY2-$this->pChartObject->GraphAreaY1-$XMargin*2)/$XDivs; }

         if ( $LineWidth == 1 )
          $this->pChartObject->drawLine($PosArray[2],$Y,$PosArray[3],$Y,$LineSettings);
         else
          $this->pChartObject->drawFilledRectangle($PosArray[2],$Y-$LineOffset,$PosArray[3],$Y+$LineOffset,$LineSettings);

         if ( $ShadowOnBoxesOnly ) { $RestoreShadow = $this->pChartObject->Shadow; $this->pChartObject->Shadow = FALSE; }

         if ( $ExtremityWidth == 1 )
          {
           $this->pChartObject->drawLine($PosArray[2],$Y-$ExtremityLength,$PosArray[2],$Y+$ExtremityLength,$ExtremitySettings);
           $this->pChartObject->drawLine($PosArray[3],$Y-$ExtremityLength,$PosArray[3],$Y+$ExtremityLength,$ExtremitySettings);
          }
         else
          {
           $this->pChartObject->drawFilledRectangle($PosArray[2],$Y-$ExtremityLength,$PosArray[2]-$ExtremityWidth,$Y+$ExtremityLength,$ExtremitySettings);
           $this->pChartObject->drawFilledRectangle($PosArray[3],$Y-$ExtremityLength,$PosArray[3]+$ExtremityWidth,$Y+$ExtremityLength,$ExtremitySettings);
          }

         if ( $ShadowOnBoxesOnly ) { $this->pChartObject->Shadow = $RestoreShadow; }

         if ( $PosArray[0] < $PosArray[1] )
          $this->pChartObject->drawFilledRectangle($PosArray[0],$Y-$BoxOffset,$PosArray[1],$Y+$BoxOffset,$BoxUpSettings);
         else
          $this->pChartObject->drawFilledRectangle($PosArray[0],$Y-$BoxOffset,$PosArray[1],$Y+$BoxOffset,$BoxDownSettings);

         $Y = $Y + $XStep;
        }
      }
    }
  }
?>