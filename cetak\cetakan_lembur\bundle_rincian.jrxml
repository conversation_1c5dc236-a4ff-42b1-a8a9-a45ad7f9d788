<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="bundle_rincian" language="groovy" pageWidth="1432" pageHeight="595" orientation="Landscape" columnWidth="1432" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="85a2f4ad-2a07-49d3-8ea5-d006a72f8f7d">
	<property name="ireport.zoom" value="1.1"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="NID" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT *

FROM dbsdm.nota_dinas_lembur ndl

WHERE ndl.STATUS != 0
AND ndl.ID = $P{NID}
-- AND peg.STATUS_PEGAWAI IN (9,10,12,1037)]]>
	</queryString>
	<field name="ID" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NOMOR_NOTA_DINAS" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NOMOR_SURAT_TUGAS" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NO_NOTA_DINAS_PERMOHONAN_BIAYA" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="PERIHAL" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="HASIL_KEGIATAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KEGIATAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="MULAI" class="java.sql.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SELESAI" class="java.sql.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="STATUS" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CATATAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CREATED_AT" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CREATED_BY" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="55" splitType="Stretch">
			<subreport>
				<reportElement x="4" y="1" width="1425" height="53" uuid="a9d6b23f-f24f-40af-be2c-206016de4bf5"/>
				<subreportParameter name="NID">
					<subreportParameterExpression><![CDATA[$P{NID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\cetakan_lembur\\RincianLembur.jasper"]]></subreportExpression>
			</subreport>
			<break>
				<reportElement x="0" y="53" width="100" height="1" uuid="1cb95d2d-1be3-4a78-9358-e44a8750b70f"/>
			</break>
		</band>
		<band height="55">
			<subreport>
				<reportElement x="4" y="1" width="1425" height="53" isPrintInFirstWholeBand="true" uuid="15ee265d-7885-4a1a-aab3-d5d257fd91a1"/>
				<subreportParameter name="NID">
					<subreportParameterExpression><![CDATA[$P{NID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\cetakan_lembur\\RincianLembur2.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
