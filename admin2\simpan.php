          	<?php

			 $serverName = "192.168.7.188"; 
                $options = array(  "UID" => "panca",  "PWD" => "pocs5",  "Database" => "dbsdm");
                $conn = sqlsrv_connect($serverName, $options);
			$kode=$_POST["kode"];
			$absen = $_POST["absen"];
			$jabatan =$_POST["jabatan"];
			$kinerja=$_POST["kinerja"];
			$indikator=$_POST["indikator"];
			$bobot=$_POST["bobot"];
			$target=$_POST["target"];
			$jumlah=count($kinerja);
			$waktu =date('Y-m-d H:i:s');
			$waktu2 =date('Y-m-d');
			$status = '1';
			$q="select top 1 kode from account order by kode desc";
			$r=sqlsrv_query($conn,$q);
			$b=sqlsrv_fetch_array($r);
			$id=$b[0]+1;
			
			$t="select kode from account where pegawai ='$kode'";
			$u=sqlsrv_query($conn,$t);
			$v=sqlsrv_fetch_array($u);
			
			
			$tsql= "Update pegawai set ABSEN='$absen' where ID='$kode'";
			 if (!sqlsrv_query($conn, $tsql)) {
            		die( print_r( sqlsrv_errors(), true));
       		 	}

			if($v[0]==0)
			{
				$query4="insert into account (KODE,PEGAWAI, USERNAME, PASSWORD, STATUS, TANGGAL, OLEH) 
			values (?,?,?,?,?,?,?)";
			$var=array($id,$kode,$absen, $absen, $status,$waktu, $kode);
             		if (!sqlsrv_query($conn, $query4, $var)) {
            			die( print_r( sqlsrv_errors(), true));
        				
			}
			}
			else
			{
				$query4= "Update account set USERNAME='$absen',PASSWORD='$absen' where PEGAWAI='$kode'";
			 if (!sqlsrv_query($conn, $query4)) {
            		die( print_r( sqlsrv_errors(), true));
       		 	}	
			}

			$t1="select id from pejabat where pegawai ='$kode'";
			$u1=sqlsrv_query($conn,$t1);
			$v1=sqlsrv_fetch_array($u1);
			
			if($v1[0]<1)
			{
			
			$a1="select top 1 id from pejabat order by id desc";
			$b1=sqlsrv_query($conn,$a1);
			$c1=sqlsrv_fetch_array($b1);
			$id2=$c1[0]+1;
			
			$qy="insert into pejabat (ID,PEGAWAI,JABATAN, TGLAKTIF, STATUS,OLEH, TANGGAL) 
			values (?,?,?,?,?,?,?)";
			$vr=array($id2,$kode,$jabatan, $waktu2, $status,$kode,$waktu);
             if (!sqlsrv_query($conn, $qy, $vr)) {
            die( print_r( sqlsrv_errors(), true));
        	}	
			}
			else
			{
				$qy= "Update pejabat set JABATAN='$jabatan' where PEGAWAI='$kode'";
			 if (!sqlsrv_query($conn, $qy)) {
            		die( print_r( sqlsrv_errors(), true));
       		 	}	
				
			}
			

			$t2="select id from kuantitas where pegawai ='$kode'";
			$u2=sqlsrv_query($conn,$t2);
			$v2=sqlsrv_fetch_array($u2);
		
			if($v2[0]>0)
			{
				$a=0;
				while ($v2=sqlsrv_fetch_array($u2))
				{
							
				
					$tsql2= "Update kuantitas set INDIKATOR='$kinerja[$a]'
					,DO='$indikator[$a]',TARGET='$target[$a]',TARGET='$target[$a]',BOBOT='$bobot[$a]'
					where ID='$v2[0]'";
				if (!sqlsrv_query($conn, $tsql2)) {
            		die( print_r( sqlsrv_errors(), true));
       		 	}
				$a++;
				}
				
			
			}
				
			
			else
			{
			$a="select top 1 id from kuantitas order by id desc";
			$b=sqlsrv_query($conn,$a);
			$c=sqlsrv_fetch_array($b);
			$id2=$c[0]+1;
			        
			for($a=0;$a<$jumlah;$a++)
			{
			
				
				if ($indikator[$a]!="")
				{
				$tsql2= "INSERT INTO kuantitas (ID,PEGAWAI,INDIKATOR,DO,TARGET,BOBOT,STATUS,OLEH,TANGGAL) 
                VALUES (?,?,?,?,?,?,?,?,?)";
     				$var2=array($id2,$kode,$kinerja[$a], $indikator[$a],$target[$a],$bobot[$a],$status,$kode,$waktu);
					 if (!sqlsrv_query($conn, $tsql2, $var2)) {
					 die( print_r( sqlsrv_errors(), true));
        			}
				}
				$id2=$id2+1;
			
			}
			}
			
			
			
      ?>
<meta http-equiv="refresh" content="0;url=daftar.php">
                    