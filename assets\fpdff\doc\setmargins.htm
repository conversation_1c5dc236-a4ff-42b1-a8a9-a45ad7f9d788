<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>SetMargins</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetMargins</h1>
<code>SetMargins(<b>float</b> left, <b>float</b> top [, <b>float</b> right])</code>
<h2>Description</h2>
Defines the left, top and right margins. By default, they equal 1 cm. Call this method to change
them.
<h2>Parameters</h2>
<dl class="param">
<dt><code>left</code></dt>
<dd>
Left margin.
</dd>
<dt><code>top</code></dt>
<dd>
Top margin.
</dd>
<dt><code>right</code></dt>
<dd>
Right margin. Default value is the left one.
</dd>
</dl>
<h2>See also</h2>
<a href="setleftmargin.htm">SetLeftMargin()</a>,
<a href="settopmargin.htm">SetTopMargin()</a>,
<a href="setrightmargin.htm">SetRightMargin()</a>,
<a href="setautopagebreak.htm">SetAutoPageBreak()</a>.
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
