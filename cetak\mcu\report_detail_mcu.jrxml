<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report_detail_mcu" language="groovy" pageWidth="700" pageHeight="1008" columnWidth="660" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="d9fec2d4-a3ed-4323-9d18-6e25a12e38b1">
	<property name="ireport.zoom" value="1.6105100000000325"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<queryString>
		<![CDATA[SELECT
    @rownum := IF(@prev_absen = pg1.ABSEN, @rownum, @rownum + 1) AS nomor_urut,
    @prev_absen := pg1.ABSEN AS current_absen,
    CURDATE() AS TANGGAL,
    pg1.ABSEN,
    dbsdm.get_namaLengkap(pg1.ID),
    q_mcu.pertanyaan,
    q_mcu.id,
    j_mcu.id_pegawai,
    CASE
        WHEN j_mcu.id_q_mcu = 1 THEN CONCAT(j_mcu.response, ' tahun')
        WHEN j_mcu.id_q_mcu = 28 THEN CONCAT(j_mcu.response, ' tahun')
        WHEN j_mcu.response = 1 THEN 'tidak'
        WHEN j_mcu.response = 2 THEN 'ya'
        ELSE j_mcu.response
    END AS response,
    j_mcu.keterangan,
    COALESCE(j_mcu.update_at, j_mcu.created_at) AS tgl_update
FROM
    (SELECT @rownum := 0, @prev_absen := NULL) r,  -- Initialize the variables
    dbsdm.tb_j_mcu j_mcu
JOIN
    dbsdm.tb_q_mcu q_mcu ON j_mcu.id_q_mcu = q_mcu.id
JOIN
    dbsdm.pegawai1 pg1 ON j_mcu.id_pegawai = pg1.ID
WHERE
    j_mcu.id_pegawai = pg1.ID
    AND j_mcu.id_q_mcu = q_mcu.id
    #AND q_mcu.id = 6 AND j_mcu.response = 2
ORDER BY
    tgl_update DESC]]>
	</queryString>
	<field name="nomor_urut" class="java.lang.String"/>
	<field name="current_absen" class="java.lang.String"/>
	<field name="TANGGAL" class="java.sql.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ABSEN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dbsdm.get_namaLengkap(pg1.ID)" class="java.lang.String"/>
	<field name="pertanyaan" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="id" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="id_pegawai" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="response" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="keterangan" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tgl_update" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<variable name="noUrut" class="java.lang.Integer" resetType="Group" resetGroup="nama" calculation="Count">
		<variableExpression><![CDATA[Boolean.TRUE]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="noHeader" class="java.lang.Integer" incrementType="Group" incrementGroup="nama" calculation="Count">
		<variableExpression><![CDATA[0]]></variableExpression>
		<initialValueExpression><![CDATA[+1]]></initialValueExpression>
	</variable>
	<group name="nama">
		<groupExpression><![CDATA[$F{dbsdm.get_namaLengkap(pg1.ID)}]]></groupExpression>
		<groupHeader>
			<band height="47">
				<textField>
					<reportElement x="126" y="0" width="248" height="20" uuid="cf01c25d-e564-4995-a5aa-7e2c6d895dd8"/>
					<textElement verticalAlignment="Top"/>
					<textFieldExpression><![CDATA[$F{dbsdm.get_namaLengkap(pg1.ID)}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="28" y="0" width="98" height="20" uuid="af983231-e719-411a-91d7-7595d45b0c6c"/>
					<textElement verticalAlignment="Top">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Nama Pegawai :]]></text>
				</staticText>
				<textField>
					<reportElement x="520" y="0" width="116" height="20" uuid="2a28a24d-3c19-493b-a5ac-d3e3576f844f"/>
					<textElement verticalAlignment="Top"/>
					<textFieldExpression><![CDATA[$F{tgl_update}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="387" y="0" width="133" height="20" uuid="6600d337-dd69-4019-87fd-94410f9b8959"/>
					<textElement verticalAlignment="Top">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Tanggal Update MCU :]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="28" y="27" width="323" height="20" uuid="23eeaa9a-f41c-4b4f-99ee-a8e7c9adf4e3"/>
					<box leftPadding="2" rightPadding="2">
						<topPen lineWidth="0.75"/>
						<leftPen lineWidth="0.75"/>
						<bottomPen lineWidth="0.75"/>
						<rightPen lineWidth="0.75"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Pertanyaan]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="27" width="28" height="20" uuid="d6c976f5-426b-4ec6-8bca-faee532e4700"/>
					<box leftPadding="2" rightPadding="2">
						<topPen lineWidth="0.75"/>
						<leftPen lineWidth="0.75"/>
						<bottomPen lineWidth="0.75"/>
						<rightPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[No.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="351" y="27" width="141" height="20" uuid="6e6201a7-f9dd-447c-b6e6-9538de23e4fc"/>
					<box leftPadding="2" rightPadding="2">
						<topPen lineWidth="0.75"/>
						<leftPen lineWidth="0.75"/>
						<bottomPen lineWidth="0.75"/>
						<rightPen lineWidth="0.75"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Jawaban]]></text>
				</staticText>
				<textField>
					<reportElement x="0" y="0" width="28" height="20" uuid="dc5acab0-31f6-407c-ae52-48ad36071260"/>
					<textFieldExpression><![CDATA[$F{nomor_urut}+". "]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="49">
				<break>
					<reportElement x="0" y="29" width="660" height="1" uuid="4434f33e-7b88-42b7-9e92-e966efb85658"/>
				</break>
			</band>
		</groupFooter>
	</group>
	<title>
		<band height="98" splitType="Stretch">
			<staticText>
				<reportElement mode="Transparent" x="0" y="17" width="608" height="17" uuid="851a9ea7-25f0-4435-af36-29b2d3b99819"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[RUMAH SAKIT KANKER "DHARMAIS"]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="34" width="608" height="17" uuid="15ce349d-557a-4f72-be0f-cdaac388fc0a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Jl. Let. Jend S. Parman Kav. 84 - 86, Slipi, Jakarta Barat 11420]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="608" height="17" uuid="09eedea9-83fc-4d2e-ab84-d44c4ea3e5ea"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[LAPORAN MCU PEGAWAI]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy">
				<reportElement mode="Transparent" x="98" y="70" width="262" height="17" uuid="3061c967-2d3a-41db-8afb-0beefa1b2ae9"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TANGGAL}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="0" y="70" width="98" height="17" uuid="1510b300-c277-465a-b7e3-f876997e40b8"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tanggal Cetak :]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="30"/>
	</pageHeader>
	<detail>
		<band height="40" splitType="Stretch">
			<textField>
				<reportElement x="351" y="20" width="141" height="20" uuid="eef34553-a673-416b-b1d0-426480407abe"/>
				<box leftPadding="2" rightPadding="2">
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textFieldExpression><![CDATA[$F{keterangan}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="28" y="0" width="323" height="40" uuid="954781a7-abb4-48e6-9eab-f4f7311a39c1"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textFieldExpression><![CDATA[$F{pertanyaan}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="351" y="0" width="141" height="20" uuid="03328280-e444-48ef-9e8d-bddbc396b528"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textFieldExpression><![CDATA[$F{response}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="28" height="40" uuid="b790a039-7a44-4986-8f83-6cab06d412c3"/>
				<box rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$V{noUrut}+"."]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
