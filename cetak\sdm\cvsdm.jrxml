<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="cvsdm" language="groovy" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="842" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="38b790aa-9be0-40e3-9c3b-1200c79a638d">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.callouts" value="##Wed May 16 17:50:22 ICT 2018"/>
	<parameter name="PID" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT pg.ABSEN,pg.NIP,pg.NAMA_LENGKAP,pg.GELAR_DEPAN,pg.GELAR_BELAKANG, jk.VARIABEL JK,pg.KTP,pg.NPWP, tl.VARIABEL TMPT_LAHIR
       , DATE_FORMAT(pg.TANGGAL_LAHIR,'%d-%m-%Y') TGL_LAHIR,ag.VARIABEL AGAMA, skw.VARIABEL SKW
		 , pg.EMAIL, pg.NOMOR_HP, pg.ALAMAT, kel.DESKRIPSI DESA,kec.DESKRIPSI KCMTN, kot.DESKRIPSI KOTA, pro.DESKRIPSI PROVINSI
       , pog.tinggi, pog.berat, pog.rambut,pog.muka,pog.kulit,pog.ciri,pog.cacat,pog.hobi
       , DATE_FORMAT(lic.created_at,'%d-%m-%Y') CREAT


		FROM dbsdm.pegawai1 pg
      LEFT JOIN dbsdm.variabel jk ON jk.ID=pg.JENIS_KELAMIN
      LEFT JOIN dbsdm.variabel tl ON tl.ID=pg.TEMPAT_LAHIR
      LEFT JOIN dbsdm.variabel ag ON ag.ID=pg.AGAMA
      LEFT JOIN dbsdm.variabel skw ON skw.ID=pg.STATUS_NIKAH

      LEFT JOIN dbsdm.profil_pegawai pog ON pog.id_pegawai=pg.ID
      LEFT JOIN dbsdm.wilayah kel ON kel.ID=pog.desa
      LEFT JOIN dbsdm.wilayah kec ON kec.ID=pog.kecamatan
      LEFT JOIN dbsdm.wilayah kot ON kot.ID=pog.kota
      LEFT JOIN dbsdm.wilayah pro ON pro.ID=pog.provinsi

      LEFT JOIN dbsdm.log_input_cv lic ON lic.user=pg.ID




      WHERE pg.ID=$P{PID}
GROUP BY pg.ID
      ]]>
	</queryString>
	<field name="ABSEN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NIP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NAMA_LENGKAP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="GELAR_DEPAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="GELAR_BELAKANG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="JK" class="java.lang.String"/>
	<field name="KTP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NPWP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TMPT_LAHIR" class="java.lang.String"/>
	<field name="TGL_LAHIR" class="java.lang.String"/>
	<field name="AGAMA" class="java.lang.String"/>
	<field name="SKW" class="java.lang.String"/>
	<field name="EMAIL" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NOMOR_HP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ALAMAT" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="DESA" class="java.lang.String"/>
	<field name="KCMTN" class="java.lang.String"/>
	<field name="KOTA" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="PROVINSI" class="java.lang.String"/>
	<field name="tinggi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="berat" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="rambut" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="muka" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="kulit" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ciri" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="cacat" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="hobi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CREAT" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="1dd829e5-ec73-467f-8ed0-9d90cc21737a"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\pendidikan.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="23414154-f432-41a9-8da5-ee6578b9bc38"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\kursus.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<break>
				<reportElement x="0" y="23" width="595" height="1" uuid="998f7561-44cc-473d-97fe-8381b136a819"/>
			</break>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="ad04c9fc-c633-46d2-a688-eac1096c27e9"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\riwayatpekerjaan.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="daa3c230-c522-483c-a7cb-a5f04b834935"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\penghargaan.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="c31aeca0-355b-4bb9-a6a8-1cebd548b15a"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\rkistri.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="d9c1b172-e5fc-4a4e-9f3e-6b9147d44545"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\rkanak.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="93d2ae24-907b-4374-aa4a-dbf26d4d6678"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\orangtua.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<break>
				<reportElement x="0" y="24" width="595" height="1" uuid="a8ea9bac-908a-47bd-9c1e-d1367fb8561b"/>
			</break>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="8b683672-5b72-4ee7-a008-1844840a433f"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\saudarakandung.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="aae0670f-1e46-4cce-bf38-90f5329089ea"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\mertua.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="842" height="50" uuid="b89a24e0-6e36-41ce-b927-4b50cec85025"/>
				<subreportParameter name="PID">
					<subreportParameterExpression><![CDATA[$P{PID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\sdm\\organisasi.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<summary>
		<band height="173">
			<staticText>
				<reportElement x="0" y="11" width="842" height="36" uuid="2fe77b2e-650e-4bdb-993d-64bbf0d30eff"/>
				<box leftPadding="3">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Top">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Demikian daftar riwayat hidup ini saya buat dengan sesungguhnya dan apabila di kemudian hari terdapat keterangan yang tidak benar saya bersedia dituntut di
muka pengadilan serta bersedia menerima segala tindakan yang diambil oleh Instansi Pemerintah. ]]></text>
			</staticText>
			<staticText>
				<reportElement x="619" y="76" width="42" height="19" uuid="a584cf3e-139d-49b6-90f0-a27a6c0de168"/>
				<box leftPadding="3">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Jakarta,]]></text>
			</staticText>
			<textField>
				<reportElement x="661" y="76" width="109" height="20" uuid="e6434f63-00e2-46fd-955e-2b3a01e7f3fb"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{CREAT}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="548" y="96" width="222" height="19" uuid="c5b77e19-771f-46fe-ab8a-5e6f82d40599"/>
				<box leftPadding="3">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Yang membuat]]></text>
			</staticText>
			<textField>
				<reportElement x="548" y="130" width="222" height="29" uuid="5d865507-9de3-40aa-aecc-6932552b4bb2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{NAMA_LENGKAP}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
