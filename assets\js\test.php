

<!DOCTYPE html>
<html>
  <head>
    <title>husnanlabs.blogspot.com</title>
    <meta charset="utf-8" />
   <link rel="stylesheet" href="awesomplete.css" />
   <script src="awesomplete/awesomplete.js" async></script>
   <link href="form_style.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript">
function add_row()
{
 $rowno=$("#employee_table tr").length;
 $rowno=$rowno+1;
 $("#employee_table tr:last").after("<tr id='row"+$rowno+"'><td><input type='text' name='age[]' placeholder='Enter Age'></td><td><input type='text' name='job[]' placeholder='Enter Job'></td><td><input type='button' value='DELETE' onclick=delete_row('row"+$rowno+"')></td></tr>");
}
function delete_row(rowno)
{
 $('#'+rowno).remove();
}
</script>
  </head>
  <body>
   
    Kota: <input class="awesomplete" type="text" data-list="Ada, Java, JavaScript, Brainfuck, LOLCODE, Node.js, Ruby on Rails" />
   <div id="wrapper">

<div id="form_div">
 <form method="post" action="store_detail.php">
  <table id="employee_table" align=center>
   <tr id="row1">
    <td><input type="text" name="age[]" placeholder="Enter Age" input class="awesomplete" type="text" data-list="Ada, Java, JavaScript, Brainfuck, LOLCODE, Node.js, Ruby on Rails"></td>
    <td><input type="text" name="job[]" placeholder="Enter Job" input class="awesomplete" type="text" data-list="Ada, Java, JavaScript, Brainfuck, LOLCODE, Node.js, Ruby on Rails"></td>
   </tr>
  </table>
  <input type="button" onclick="add_row();" value="ADD ROW">
  <input type="submit" name="submit_row" value="SUBMIT">
 </form>
</div>

</div>
    
  </body>  
</html>  