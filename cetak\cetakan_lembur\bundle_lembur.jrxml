<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="bundle_lembur" language="groovy" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="fee5b8ac-fde9-4a7b-ad22-79c95f1a4fee">
	<property name="ireport.zoom" value="0.75"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="1056"/>
	<parameter name="NID" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT
SUM(IF(lp.ID_LEMBUR IS NOT NULL AND lp.STATUS NOT IN ('0','3'), 1, null)) JML_PEGAWAI
, ndl.ID
, lp.ID_LEMBUR
, ndl.NOMOR_NOTA_DINAS
, ndl.STATUS
, lp.STATUS

FROM dbsdm.nota_dinas_lembur ndl

LEFT JOIN dbsdm.lembur_pegawai lp ON lp.ID_NOTADINAS = ndl.ID
-- LEFT JOIN dbsdm.jabatan j ON j.ID = lp.PARENT
-- LEFT JOIN dbsdm.jabatan j2 ON j2.ID = SUBSTR(lp.PARENT,1,6) AND j2.STATUS = 1
-- LEFT JOIN dbsdm.jabatan j3 ON j3.ID = SUBSTR(lp.PARENT,1,8) AND j3.STATUS = 1
-- LEFT JOIN dbsdm.pejabat1 pj1 ON pj1.JABATAN = j.ID AND (pj1.STATUS = 1 AND j.STATUS = 1)
-- LEFT JOIN dbsdm.pejabat1 pj2 ON pj2.JABATAN = j3.ID AND (pj2.STATUS = 1 AND j3.STATUS = 1)

WHERE ndl.STATUS != 0
-- AND lp.STATUS NOT IN ('0','3')
AND ndl.ID = $P{NID}

-- GROUP BY ndl.ID]]>
	</queryString>
	<field name="JML_PEGAWAI" class="java.math.BigDecimal"/>
	<field name="ID" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ID_LEMBUR" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NOMOR_NOTA_DINAS" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="STATUS" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="842" splitType="Stretch">
			<subreport runToBottom="false">
				<reportElement x="0" y="0" width="595" height="50" uuid="094d8e23-17b1-4414-adf6-a7a1f1732563"/>
				<subreportParameter name="NID">
					<subreportParameterExpression><![CDATA[$P{NID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\cetakan_lembur\\nota_dinas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="842">
			<subreport runToBottom="false">
				<reportElement x="0" y="0" width="595" height="50" uuid="30b08273-a28e-4b50-ae4a-d4060f60eac5">
					<printWhenExpression><![CDATA[$F{JML_PEGAWAI} > 2]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="NID">
					<subreportParameterExpression><![CDATA[$P{NID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\cetakan_lembur\\surat_tugas_3.jasper"]]></subreportExpression>
			</subreport>
			<subreport runToBottom="false">
				<reportElement x="0" y="0" width="595" height="50" uuid="2b45d7a9-2bc5-4875-99c1-d8e6a44475bd">
					<printWhenExpression><![CDATA[$F{JML_PEGAWAI} == 2]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="NID">
					<subreportParameterExpression><![CDATA[$P{NID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\cetakan_lembur\\surat_tugas_2.jasper"]]></subreportExpression>
			</subreport>
			<subreport runToBottom="false">
				<reportElement x="0" y="0" width="595" height="50" uuid="a3031909-4ef9-4ec6-b089-51b8f3f776a8">
					<printWhenExpression><![CDATA[$F{JML_PEGAWAI} == 1]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="NID">
					<subreportParameterExpression><![CDATA[$P{NID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\cetakan_lembur\\surat_tugas_1.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="842">
			<subreport>
				<reportElement x="0" y="0" width="595" height="50" uuid="327aad6e-4d0c-4103-a5d7-8bb44568d438"/>
				<subreportParameter name="NID">
					<subreportParameterExpression><![CDATA[$P{NID}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\cetak\\cetakan_lembur\\notarincian.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
