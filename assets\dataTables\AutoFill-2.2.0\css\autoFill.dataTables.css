div.dt-autofill-handle {
  position: absolute;
  height: 8px;
  width: 8px;
  z-index: 102;
  box-sizing: border-box;
  border: 1px solid #316ad1;
  background: linear-gradient(to bottom, #abcffb 0%, #4989de 100%);
}

div.dt-autofill-select {
  position: absolute;
  z-index: 1001;
  background-color: #4989de;
  background-image: repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(255, 255, 255, 0.5) 5px, rgba(255, 255, 255, 0.5) 10px);
}
div.dt-autofill-select.top, div.dt-autofill-select.bottom {
  height: 3px;
  margin-top: -1px;
}
div.dt-autofill-select.left, div.dt-autofill-select.right {
  width: 3px;
  margin-left: -1px;
}

div.dt-autofill-list {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 500px;
  margin-left: -250px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 0 5px #555;
  border: 2px solid #444;
  z-index: 11;
  box-sizing: border-box;
  padding: 1.5em 2em;
}
div.dt-autofill-list ul {
  display: table;
  margin: 0;
  padding: 0;
  list-style: none;
  width: 100%;
}
div.dt-autofill-list ul li {
  display: table-row;
}
div.dt-autofill-list ul li:last-child div.dt-autofill-question, div.dt-autofill-list ul li:last-child div.dt-autofill-button {
  border-bottom: none;
}
div.dt-autofill-list ul li:hover {
  background-color: #f6f6f6;
}
div.dt-autofill-list div.dt-autofill-question {
  display: table-cell;
  padding: 0.5em 0;
  border-bottom: 1px solid #ccc;
}
div.dt-autofill-list div.dt-autofill-question input[type=number] {
  padding: 6px;
  width: 30px;
  margin: -2px 0;
}
div.dt-autofill-list div.dt-autofill-button {
  display: table-cell;
  padding: 0.5em 0;
  border-bottom: 1px solid #ccc;
}
div.dt-autofill-list div.dt-autofill-button button {
  color: white;
  margin: 0;
  padding: 6px 12px;
  text-align: center;
  border: 1px solid #2e6da4;
  background-color: #337ab7;
  border-radius: 4px;
  cursor: pointer;
  vertical-align: middle;
}

div.dt-autofill-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  background: radial-gradient(ellipse farthest-corner at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
  z-index: 10;
}
