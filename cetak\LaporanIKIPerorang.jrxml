<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="LaporanIKIPerorang" language="groovy" pageWidth="595" pageHeight="862" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" uuid="7b68c18a-880a-4748-bcdc-39b2c9c56248">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="203"/>
	<property name="ireport.y" value="391"/>
	<parameter name="TAHUN" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="PEGAWAI" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT u.UNIT,p.NIP,
dbsdm.get_namaLengkap(p.ID) NAMA_A,
j.JABATAN,
p1.NIP NIP_ATL,
dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,
j1.JABATAN JABATAN_ATL,
p2.NIP NIP_AT,
dbsdm.get_namaLengkap(p2.ID) NAMA_AT,
j2.JABATAN JABATAN_AT, i.IKI,
kuan.INDIKATOR, kuan.DO DO, SUM(kuan.TARGET), SUM(kuai.CAPAIAN), SUM(ROUND(kuan.BOBOT,3)) BOBOT, SUM(ROUND((kuai.CAPAIAN/kuan.TARGET)*kuan.BOBOT,3)) NILAI, 'A' KET, 'KUANTITAS' KET1, 'A1' KET2,
IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),
IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE
FROM pegawai1 p
LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')
LEFT JOIN jabatan j ON pj.JABATAN = j.ID
LEFT JOIN jabatan j1 ON j.PARENT = j1.ID
LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID
LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')
LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')
LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID
LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID
LEFT JOIN unit u ON j.UNIT = u.ID
LEFT JOIN iki i ON p.ID = i.DINILAI
LEFT JOIN kuantitasiki kuai ON i.ID = kuai.IKI
LEFT JOIN kuantitas kuan ON kuai.KUANTITAS = kuan.ID
WHERE p.ID = $P{PEGAWAI} AND i.ID LIKE '2019%'

GROUP BY kuan.ID

UNION

SELECT u.UNIT,p.NIP,
dbsdm.get_namaLengkap(p.ID) NAMA_A,
j.JABATAN,
p1.NIP NIP_ATL,
dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,
j1.JABATAN JABATAN_ATL,
p2.NIP NIP_AT,
dbsdm.get_namaLengkap(p2.ID) NAMA_AT,
j2.JABATAN JABATAN_AT, i.IKI,
kual.INDIKATOR, kual.DO DO, kual.TARGET, kuali.CAPAIAN, ROUND(kual.BOBOT,3) BOBOT, ROUND((kuali.CAPAIAN/kual.TARGET)*kual.BOBOT,3) NILAI, 'B' KET, 'KUALITAS' KET1, 'A1' KET2,
IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),
IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE
FROM pegawai1 p
LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')
LEFT JOIN jabatan j ON pj.JABATAN = j.ID
LEFT JOIN jabatan j1 ON j.PARENT = j1.ID
LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID
LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')
LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')
LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID
LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID
LEFT JOIN unit u ON j.UNIT = u.ID
LEFT JOIN iki i ON p.ID = i.DINILAI
LEFT JOIN kualitasiki kuali ON i.ID = kuali.IKI
LEFT JOIN kualitas kual ON kuali.KUALITAS = kual.ID
WHERE p.ID = $P{PEGAWAI} AND i.ID LIKE '2019%'

GROUP BY kual.ID

UNION
SELECT u.UNIT,p.NIP,
dbsdm.get_namaLengkap(p.ID) NAMA_A,
j.JABATAN,
p1.NIP NIP_ATL,
dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,
j1.JABATAN JABATAN_ATL,
p2.NIP NIP_AT,
dbsdm.get_namaLengkap(p2.ID) NAMA_AT,
j2.JABATAN JABATAN_AT, i.IKI,
per.INDIKATOR, per.DO DO, per.TARGET, peri.CAPAIAN, ROUND(per.BOBOT,3) BOBOT, ROUND((peri.CAPAIAN/per.TARGET)*per.BOBOT,3) NILAI, 'C' KET, 'PERILAKU' KET1, 'A2' KET2,
IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),
IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),
IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE
FROM pegawai1 p
LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')
LEFT JOIN jabatan j ON pj.JABATAN = j.ID
LEFT JOIN jabatan j1 ON j.PARENT = j1.ID
LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID
LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')
LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')
LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID
LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID
LEFT JOIN unit u ON j.UNIT = u.ID
LEFT JOIN iki i ON p.ID = i.DINILAI
LEFT JOIN perilakuiki peri ON i.ID = peri.IKI
LEFT JOIN perilaku per ON peri.PERILAKU = per.ID
WHERE p.ID =$P{PEGAWAI} AND i.ID LIKE '2019%'

GROUP BY per.ID]]>
	</queryString>
	<field name="UNIT" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NIP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NAMA_A" class="java.lang.String"/>
	<field name="JABATAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NIP_ATL" class="java.lang.String"/>
	<field name="NAMA_ATL" class="java.lang.String"/>
	<field name="JABATAN_ATL" class="java.lang.String"/>
	<field name="NIP_AT" class="java.lang.String"/>
	<field name="NAMA_AT" class="java.lang.String"/>
	<field name="JABATAN_AT" class="java.lang.String"/>
	<field name="IKI" class="java.lang.Double">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="INDIKATOR" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="DO" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SUM(kuan.TARGET)" class="java.math.BigDecimal"/>
	<field name="SUM(kuai.CAPAIAN)" class="java.lang.Double"/>
	<field name="BOBOT" class="java.lang.Double">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NILAI" class="java.lang.Double"/>
	<field name="KET" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KET1" class="java.lang.String"/>
	<field name="KET2" class="java.lang.String"/>
	<field name="PERIODE" class="java.lang.String">
		<fieldDescription><![CDATA[periode jangka waktunya]]></fieldDescription>
	</field>
	<variable name="hit1" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{BOBOT}]]></variableExpression>
	</variable>
	<variable name="BOBOT_1" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{BOBOT}]]></variableExpression>
	</variable>
	<variable name="NILAI_1" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI}]]></variableExpression>
	</variable>
	<variable name="hit2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI}]]></variableExpression>
	</variable>
	<variable name="jum_1" class="java.lang.String">
		<variableExpression><![CDATA["JUMLAH " + $F{KET1}]]></variableExpression>
	</variable>
	<group name="ket2">
		<groupExpression><![CDATA[$F{UNIT}]]></groupExpression>
		<groupFooter>
			<band height="167">
				<frame>
					<reportElement x="0" y="19" width="555" height="148" uuid="028649b6-89ca-4b23-86cc-578420d04d0b"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textField isStretchWithOverflow="true">
						<reportElement stretchType="RelativeToTallestObject" x="361" y="31" width="155" height="16" uuid="bd312b1d-edcf-4a5b-baa9-2c1fa2539006"/>
						<textElement>
							<font fontName="Calibri" size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{JABATAN_ATL}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="361" y="11" width="122" height="20" uuid="d5e037b3-f2ae-4e12-8880-4eda01d647b4"/>
						<textElement verticalAlignment="Bottom">
							<font fontName="Calibri" size="7"/>
						</textElement>
						<text><![CDATA[Atasan Langsung]]></text>
					</staticText>
					<textField isStretchWithOverflow="true">
						<reportElement stretchType="RelativeToTallestObject" x="361" y="99" width="175" height="20" uuid="4a17adb6-9f16-4d90-9a8e-31dd57a1e80c"/>
						<textElement verticalAlignment="Bottom">
							<font fontName="Calibri" size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{NAMA_ATL}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="361" y="119" width="28" height="20" uuid="31baba3e-7aa2-4a72-9340-b401e8d5769c"/>
						<textElement textAlignment="Right" verticalAlignment="Top">
							<font fontName="Calibri" size="7"/>
						</textElement>
						<text><![CDATA[NIP :]]></text>
					</staticText>
					<textField isBlankWhenNull="true">
						<reportElement x="389" y="119" width="100" height="20" uuid="b1177bd1-a662-4e2d-b6a6-81fe1d1d6897"/>
						<textElement verticalAlignment="Top">
							<font fontName="Calibri" size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{NIP_ATL}]]></textFieldExpression>
					</textField>
				</frame>
			</band>
		</groupFooter>
	</group>
	<group name="ket1">
		<groupExpression><![CDATA[$F{KET2}]]></groupExpression>
		<groupFooter>
			<band height="16">
				<textField>
					<reportElement x="0" y="0" width="466" height="16" uuid="ef4efb67-a750-419f-96fd-5a1894eb0d10"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET2}.equals( 'A1' ) ? 'JUMLAH KUANTITAS DAN KUALITAS' : 'TOTAL NILAI KINERJA INDIVIDU']]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="466" y="0" width="40" height="16" uuid="f039afa7-18e7-4bcf-8c58-273b0cfc7a69"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{hit1}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="506" y="0" width="49" height="16" uuid="6d450c61-38ac-4efa-a027-2d3635f40ab2"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET2} == 'A1' ? $V{hit2} : $F{IKI}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="ket">
		<groupExpression><![CDATA[$F{KET}]]></groupExpression>
		<groupHeader>
			<band height="10">
				<textField>
					<reportElement mode="Opaque" x="0" y="0" width="33" height="10" backcolor="#FFCC99" uuid="738387a8-808d-4aca-a1ea-f0e32cc789ce"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="33" y="0" width="522" height="10" backcolor="#FFCC99" uuid="a903d139-b25e-4035-9335-2881bb90a8ca"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET1}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<rectangle>
					<reportElement x="0" y="0" width="33" height="17" uuid="a94f1155-e098-4ee7-939c-782769bd40ac"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="466" y="0" width="40" height="17" uuid="8375e412-726a-45fa-89d6-0f1edc5c4203"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BOBOT_1}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="506" y="0" width="49" height="17" uuid="cd91d5f6-dadf-4dd6-b085-42d7fed47cc2"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NILAI_1}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="33" y="0" width="433" height="17" uuid="067d99a4-5790-48c2-bb1b-c1cb1add4e6e"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
						<paragraph leftIndent="5"/>
					</textElement>
					<text><![CDATA[JUMLAH]]></text>
				</staticText>
				<textField>
					<reportElement x="76" y="0" width="104" height="17" uuid="d79dd36e-9d49-4f6a-9632-e44dbfea0d91"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="153">
			<staticText>
				<reportElement x="0" y="72" width="555" height="18" uuid="54c2021b-4117-4986-b655-47e673fd4351"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[PENILAIAN KINERJA]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="91" width="64" height="10" uuid="50f80c44-7ca6-48cc-9f0a-b903dd4379e7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NAMA]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="101" width="64" height="10" uuid="d72ae43e-3552-4397-a5b7-b245f6f24461"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NIP]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="111" width="64" height="10" uuid="6e7256d9-8e75-48ed-953f-eb5c07494104"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[JABATAN]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="121" width="64" height="10" uuid="19ed6147-5447-4f20-bd90-ee3bd12252a0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[UNIT KERJA]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="131" width="64" height="10" uuid="0a8b8b4b-f0ea-45b8-a8e4-8992a83fb80e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[PERIODE]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="91" width="14" height="10" uuid="5970554d-039d-4679-aa03-d58e3b30e4fb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="101" width="14" height="10" uuid="832cdd7b-c440-4ec9-ad59-9f5015a68f6a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="111" width="14" height="10" uuid="aa2d23ec-0343-4ae0-8500-7997c45be07b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="121" width="14" height="10" uuid="2fad4568-6a35-46cd-9937-2a2105f4e364"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="131" width="14" height="10" uuid="dd022b2a-c4f3-49d6-aa2c-fd1e6b7a3ece"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="91" width="279" height="10" uuid="061d1aa8-18dc-4925-82db-25d6110a19bd"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NAMA_A}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="101" width="279" height="10" uuid="7debfb61-c418-4f0c-904b-00c523297dc8"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NIP}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="111" width="279" height="10" uuid="643e4a25-0588-4f72-9554-9c79dcab2453"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{JABATAN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="121" width="279" height="10" uuid="2f59505c-5b7b-487b-a105-5f7e4fc94bbe"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{UNIT}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="442" y="14" width="41" height="47" uuid="3df54cae-dc9b-4f14-bc8c-e420ea3e1e76"/>
				<imageExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\laporan\\logo-dharmais.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="76" y="14" width="47" height="33" uuid="bbe73d45-82bc-4d6d-82a8-d49fe953b5be"/>
				<imageExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\laporan\\logo-kemenkes.png"]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="0" y="5" width="555" height="25" uuid="ee0269c0-47bc-4358-8079-f8b94d711df0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[RUMAH SAKIT KANKER "DHARMAIS"]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="30" width="555" height="17" uuid="8f63c49a-d629-49de-ab19-271a2142ca00"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Jl. Let. Jend. S. Parman Kav. 84-86, Slipi, Jakarta Barat 11420]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="46" width="555" height="17" uuid="2beb8e18-4fec-424e-91e1-291a52b0d4c7"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Telepon : (021) 5681570 Faximile : (021) 5681579]]></text>
			</staticText>
			<line>
				<reportElement x="5" y="72" width="550" height="1" uuid="568e5671-eb0a-4884-8e13-e61477cc8b7d"/>
				<graphicElement>
					<pen lineStyle="Double"/>
				</graphicElement>
			</line>
		</band>
	</title>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="506" y="0" width="49" height="12" backcolor="#FFFF99" uuid="d37b28db-e702-43af-9038-6ae5b7927885"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NILAI HASIL]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="12" width="33" height="9" backcolor="#FFFF99" uuid="bcc674bb-118e-4b64-bef7-dcab521e64a6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[1]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="33" height="12" backcolor="#FFFF99" uuid="ad0d4fc2-9ab6-4a4e-b685-ddba1ec9e8a1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NO	]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="33" y="12" width="154" height="9" backcolor="#FFFF99" uuid="70a8a398-0d98-4dbe-8a18-b39c556dc532"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[2]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="33" y="0" width="154" height="12" backcolor="#FFFF99" uuid="2c34f8fe-2237-4b70-832b-f1a643e00f0e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[KINERJA]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="187" y="12" width="164" height="9" backcolor="#FFFF99" uuid="360f6b02-2284-43c3-8200-097d0094dc4a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[3]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="351" y="12" width="57" height="9" backcolor="#FFFF99" uuid="3654b5e6-bbb6-4d62-89be-2bfeae2a4bff"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[4]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="408" y="0" width="58" height="12" backcolor="#FFFF99" uuid="d670437c-149a-4eb6-ad4d-20a628917d5a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[CAPAIAN]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="408" y="12" width="58" height="9" backcolor="#FFFF99" uuid="0e98a9c4-12d9-478b-a862-f00f84797630"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[5]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="466" y="12" width="40" height="9" backcolor="#FFFF99" uuid="3fcacf2d-1ce1-435c-aedd-6058cbc1cae9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[6]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="506" y="12" width="49" height="9" backcolor="#FFFF99" uuid="846e7020-e5ec-4670-8f21-4f48b1c01209"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[(5/4)*6]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="466" y="0" width="40" height="12" backcolor="#FFFF99" uuid="79c2cb1e-ebb1-405c-a190-73d730ea2f08"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[BOBOT %]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="187" y="0" width="164" height="12" backcolor="#FFFF99" uuid="80628627-d726-408b-994d-70c7eac78e15"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[INDIKATOR KINERJA	]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="351" y="0" width="57" height="12" backcolor="#FFFF99" uuid="705b6e90-d25e-4dbd-b3cb-4911cc61588a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[TARGET]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="51" y="0" width="136" height="14" uuid="1e1f0d5f-af14-4fdf-814c-940eeb785615"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="20"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{INDIKATOR}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="187" y="0" width="164" height="14" uuid="2fe0c531-a715-4dd4-a402-fbb685cc248d"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="20"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="408" y="0" width="58" height="14" uuid="856dd53f-882c-44c6-950d-5cc04469d8af"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SUM(kuai.CAPAIAN)}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="466" y="0" width="40" height="14" uuid="3b539ace-920f-4ba9-b077-82c300f5e43f"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BOBOT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="506" y="0" width="49" height="14" uuid="8594df92-4133-43bb-af73-1eeb7ef6562d"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
			</textField>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="33" height="14" uuid="6497cfa3-ca40-44ba-b2db-f9ff020bf46f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="33" y="0" width="18" height="14" uuid="d05c7994-c515-4eff-b293-e70003234986"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isStrikeThrough="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{ket_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="351" y="0" width="57" height="14" uuid="16faebd3-5255-49a7-b06d-d2bb7e32cd84"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SUM(kuan.TARGET)}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
