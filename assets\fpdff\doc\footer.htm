<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Footer</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Footer</h1>
<code>Footer()</code>
<h2>Description</h2>
This method is used to render the page footer. It is automatically called by AddPage() and
Close() and should not be called directly by the application. The implementation in FPDF is
empty, so you have to subclass it and override the method if you want a specific processing.
<h2>Example</h2>
<div class="doc-source">
<pre><code>class PDF extends FPDF
{
function Footer()
{
    //Go to 1.5 cm from bottom
    $this-&gt;SetY(-15);
    //Select Arial italic 8
    $this-&gt;SetFont('Arial','I',8);
    //Print centered page number
    $this-&gt;Cell(0,10,'Page '.$this-&gt;PageNo(),0,0,'C');
}
}</code></pre>
</div>
<h2>See also</h2>
<a href="header.htm">Header()</a>.
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
