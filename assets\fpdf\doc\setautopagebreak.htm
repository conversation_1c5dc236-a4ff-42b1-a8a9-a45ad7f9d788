<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>SetAutoPageBreak</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetAutoPageBreak</h1>
<code>SetAutoPageBreak(<b>boolean</b> auto [, <b>float</b> margin])</code>
<h2>Description</h2>
Enables or disables the automatic page breaking mode. When enabling, the second parameter is
the distance from the bottom of the page that defines the triggering limit. By default, the
mode is on and the margin is 2 cm.
<h2>Parameters</h2>
<dl class="param">
<dt><code>auto</code></dt>
<dd>
Boolean indicating if mode should be on or off.
</dd>
<dt><code>margin</code></dt>
<dd>
Distance from the bottom of the page.
</dd>
</dl>
<h2>See also</h2>
<a href="cell.htm">Cell</a>,
<a href="multicell.htm">MultiCell</a>,
<a href="acceptpagebreak.htm">AcceptPageBreak</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
