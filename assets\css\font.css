/* latin-ext */
@font-face {
  font-family: 'Ruda';
  font-style: normal;
  font-weight: 400;
  src: local('<PERSON>uda Regular'), local('Ruda-Regular'), url(text/1sL847GoOH8xYu1qOqsKrw.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Ruda';
  font-style: normal;
  font-weight: 400;
  src: local('Ruda Regular'), local('Ruda-Regular'), url(text/T9zdIB5JGDJjRO8KNoV_pA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2212, U+2215;
}
/* latin-ext */
@font-face {
  font-family: 'Ruda';
  font-style: normal;
  font-weight: 700;
  src: local('Ruda Bold'), local('Ruda-Bold'), url(text/Cq8KyqhCX-f1J9BsOyq_FvY6323mHUZFJMgTvxaG2iE.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Ruda';
  font-style: normal;
  font-weight: 700;
  src: local('Ruda Bold'), local('Ruda-Bold'), url(text/ioyuq9I92dSCu7pGUbx7zA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2212, U+2215;
}
/* latin-ext */
@font-face {
  font-family: 'Ruda';
  font-style: normal;
  font-weight: 900;
  src: local('Ruda Black'), local('Ruda-Black'), url(text/AV-eDyU_-j5hBe_Ff6xI1_Y6323mHUZFJMgTvxaG2iE.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Ruda';
  font-style: normal;
  font-weight: 900;
  src: local('Ruda Black'), local('Ruda-Black'), url(text/9WoKvbp3ZUwn9qM5AIuMOg.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2212, U+2215;
}