<?php
//koneksi ke database, username,password  dan namadatabase menyesuaikan 
// mysql_connect('localhost', 'username', 'password');
// mysql_select_db('namadatabase');
   $serverName = "*************"; 
                $options = array(  "UID" => "panca",  "PWD" => "pocs5",  "Database" => "dbsdm");
                $conn = sqlsrv_connect($serverName, $options);
//memanggil file excel_reader
require "excel_reader.php";
 
//jika tombol import ditekan
if(isset($_POST['submit'])){
 
    $target = basename($_FILES['filepegawaiall']['name']) ;
    move_uploaded_file($_FILES['filepegawaiall']['tmp_name'], $target);
 
// tambahkan baris berikut untuk mencegah error is not readable
    chmod($_FILES['filepegawaiall']['name'],0777);
    
    $data = new Spreadsheet_Excel_Reader($_FILES['filepegawaiall']['name'],false);
    
//    menghitung jumlah baris file xls
    $baris = $data->rowcount($sheet_index=0);
    
//    jika kosongkan data dicentang jalankan kode berikut
    $drop = isset( $_POST["drop"] ) ? $_POST["drop"] : 0 ;
    if($drop == 1){
//             kosongkan tabel pegawai
             $truncate ="TRUNCATE TABLE pegawai";
            // mysql_query($truncate);
    };
    $waktu =date('Y-m-d H:i:s');
	$status='1';
	$oleh='55555';
	$q="select top 1 kode from account order by kode desc";
			$r=sqlsrv_query($conn,$q);
			$b=sqlsrv_fetch_array($r);
			$id=$b[0]+1;
			
			$a1="select top 1 id from pejabat order by id desc";
			$b1=sqlsrv_query($conn,$a1);
			$c1=sqlsrv_fetch_array($b1);
			$id2=$c1[0]+1;
			
			
//    import data excel mulai baris ke-2 (karena tabel xls ada header pada baris 1)
    for ($i=2; $i<=$baris; $i++)
    {
	
		$kode= $data->val($i,1);
		$absen= $data->val($i,2);
		$jabatan=$data->val($i,3);
		
		$tsql= "Update pegawai set ABSEN='$absen' where ID='$kode'";
			 if (!sqlsrv_query($conn, $tsql)) {
            die( print_r( sqlsrv_errors(), true));
       		 }
			$query4="insert into account (KODE,PEGAWAI, USERNAME, PASSWORD, STATUS, TANGGAL, OLEH) 
			values (?,?,?,?,?,?,?)";
			$var=array($id,$kode,$absen, $absen, $status,$waktu, $oleh);
             if (!sqlsrv_query($conn, $query4, $var)) {
            die( print_r( sqlsrv_errors(), true));
        	}	
			$qy="insert into pejabat (ID,PEGAWAI,JABATAN, TGLAKTIF, STATUS,OLEH, TANGGAL) 
			values (?,?,?,?,?,?,?)";
			$vr=array($id2,$kode,$jabatan, $waktu, $status,$oleh,$waktu);
             if (!sqlsrv_query($conn, $qy, $vr)) {
            die( print_r( sqlsrv_errors(), true));
        	}	
   $id++;$id2++;
   $hasil=$i;
    }
    
    if(!$hasil){
//          jika import gagal
          die(mysql_error());
      }else{
//          jika impor berhasil
          echo "Data berhasil diimpor.";
    }
    
//    hapus file xls yang udah dibaca
    unlink($_FILES['filepegawaiall']['name']);
}
 
?>
 
<form name="myForm" id="myForm" onSubmit="return validateForm()" action="upload_account.php" method="post" enctype="multipart/form-data">
    <input type="file" id="filepegawaiall" name="filepegawaiall" />
    <input type="submit" name="submit" value="Import" /><br/>
    
</form>
 
<script type="text/javascript">
//    validasi form (hanya file .xls yang diijinkan)
    function validateForm()
    {
        function hasExtension(inputID, exts) {
            var fileName = document.getElementById(inputID).value;
            return (new RegExp('(' + exts.join('|').replace(/\./g, '\\.') + ')$')).test(fileName);
        }
 
        if(!hasExtension('filepegawaiall', ['.xls'])){
            alert("Hanya file XLS (Excel 2003) yang diijinkan.");
            return false;
        }
    }
</script>