<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="LaporanIKITahunanPerorang" language="groovy" pageWidth="1508" pageHeight="595" orientation="Landscape" columnWidth="1468" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" uuid="7b68c18a-880a-4748-bcdc-39b2c9c56248">
	<property name="ireport.zoom" value="1.3636363636363722"/>
	<property name="ireport.x" value="1121"/>
	<property name="ireport.y" value="0"/>
	<parameter name="TAHUN" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="PEGAWAI" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT a.UNIT, a.NIP, a.NAMA_A, a.JABATAN, a.NIP_ATL, a.NAMA_ATL, a.JABATAN_ATL, a.IKI

	, a.INDIKATOR, a.DO, a.KET

	, a.KET1, a.KET2, MAX(a.TAHUN_ANGKA) PERIODE



	, SUM(a.TARGET) TOTAL_TARGET

	, SUM(a.CAPAIAN) TOTAL_CAPAIAN

	, a.BOBOT BOBOT

	, SUM(a.NILAI) TOTAL_NILAI

	, SUM(IF(a.PERIODE_ANGKA=1,a.CAPAIAN,0)) CAPAIAN_JANUARI

	, SUM(IF(a.PERIODE_ANGKA=1,a.NILAI,0)) NILAI_JANUARI



	, SUM(IF(a.PERIODE_ANGKA=2,a.CAPAIAN,0)) CAPAIAN_FEB

	, SUM(IF(a.PERIODE_ANGKA=2,a.NILAI,0)) NILAI_FEB



	, SUM(IF(a.PERIODE_ANGKA=3,a.CAPAIAN,0)) CAPAIAN_MARET

	, SUM(IF(a.PERIODE_ANGKA=3,a.NILAI,0)) NILAI_MARET



	, SUM(IF(a.PERIODE_ANGKA=4,a.CAPAIAN,0)) CAPAIAN_APRIL

	, SUM(IF(a.PERIODE_ANGKA=4,a.NILAI,0)) NILAI_APRIL



	, SUM(IF(a.PERIODE_ANGKA=5,a.CAPAIAN,0)) CAPAIAN_MAY

	, SUM(IF(a.PERIODE_ANGKA=5,a.NILAI,0)) NILAI_MAY



	, SUM(IF(a.PERIODE_ANGKA=6,a.CAPAIAN,0)) CAPAIAN_JUNE

	, SUM(IF(a.PERIODE_ANGKA=6,a.NILAI,0)) NILAI_JUNE



	, SUM(IF(a.PERIODE_ANGKA=7,a.CAPAIAN,0)) CAPAIAN_JULY

	, SUM(IF(a.PERIODE_ANGKA=7,a.NILAI,0)) NILAI_JULY



	, SUM(IF(a.PERIODE_ANGKA=8,a.CAPAIAN,0)) CAPAIAN_AUG

	, SUM(IF(a.PERIODE_ANGKA=8,a.NILAI,0)) NILAI_AUG



	, SUM(IF(a.PERIODE_ANGKA=9,a.CAPAIAN,0)) CAPAIAN_SEPT

	, SUM(IF(a.PERIODE_ANGKA=9,a.NILAI,0)) NILAI_SEPT



	, SUM(IF(a.PERIODE_ANGKA=10,a.CAPAIAN,0)) CAPAIAN_OCT

	, SUM(IF(a.PERIODE_ANGKA=10,a.NILAI,0)) NILAI_OCT



	, SUM(IF(a.PERIODE_ANGKA=11,a.CAPAIAN,0)) CAPAIAN_NOV

	, SUM(IF(a.PERIODE_ANGKA=11,a.NILAI,0)) NILAI_NOV



	, SUM(IF(a.PERIODE_ANGKA=12 AND a.TAHUN_ANGKA=$P{TAHUN}, a.CAPAIAN,0)) CAPAIAN_DES

	, SUM(IF(a.PERIODE_ANGKA=12 AND a.TAHUN_ANGKA=$P{TAHUN},a.NILAI,0)) NILAI_DES



FROM



(SELECT u.UNIT,p.NIP,

	dbsdm.get_namaLengkap(p.ID) NAMA_A,

	j.JABATAN,

	p1.NIP NIP_ATL,

	dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,

	j1.JABATAN JABATAN_ATL,

	p2.NIP NIP_AT,

	dbsdm.get_namaLengkap(p2.ID) NAMA_AT,

	j2.JABATAN JABATAN_AT, i.IKI,

	kuan.INDIKATOR, kuan.DO DO, kuan.TARGET, kuai.CAPAIAN, ROUND(kuan.BOBOT,3) BOBOT

	, ROUND((kuai.CAPAIAN/kuan.TARGET)*kuan.BOBOT,3) NILAI, 'A' KET, 'KUANTITAS' KET1, 'A1' KET2



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL')))))))))))) PERIODE



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',12,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',1,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',2,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',3,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',4,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',5,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',6,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',7,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',8,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',9,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',10,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',11,'GAGAL')))))))))))) PERIODE_ANGKA



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))TAHUN_ANGKA



FROM pegawai1 p



	LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')

	LEFT JOIN jabatan j ON pj.JABATAN = j.ID

	LEFT JOIN jabatan j1 ON j.PARENT = j1.ID

	LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID

	LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')

	LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')

	LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID

	LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID

	LEFT JOIN unit u ON j.UNIT = u.ID

	LEFT JOIN iki i ON p.ID = i.DINILAI

	LEFT JOIN kuantitasiki kuai ON i.ID = kuai.IKI

	LEFT JOIN kuantitas kuan ON kuai.KUANTITAS = kuan.ID



WHERE p.ID = $P{PEGAWAI} #AND i.ID LIKE '2019%'



UNION



SELECT u.UNIT,p.NIP,

	dbsdm.get_namaLengkap(p.ID) NAMA_A,

	j.JABATAN,

	p1.NIP NIP_ATL,

	dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,

	j1.JABATAN JABATAN_ATL,

	p2.NIP NIP_AT,

	dbsdm.get_namaLengkap(p2.ID) NAMA_AT,

	j2.JABATAN JABATAN_AT, i.IKI,

	kual.INDIKATOR, kual.DO DO, kual.TARGET, kuali.CAPAIAN, ROUND(kual.BOBOT,3) BOBOT

	, ROUND((kuali.CAPAIAN/kual.TARGET)*kual.BOBOT,3) NILAI, 'B' KET, 'KUALITAS' KET1, 'A1' KET2



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',12,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',1,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',2,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',3,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',4,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',5,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',6,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',7,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',8,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',9,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',10,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',11,'GAGAL')))))))))))) PERIODE_ANGKA



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))TAHUN_ANGKA





FROM pegawai1 p



	LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')

	LEFT JOIN jabatan j ON pj.JABATAN = j.ID

	LEFT JOIN jabatan j1 ON j.PARENT = j1.ID

	LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID

	LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')

	LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')

	LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID

	LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID

	LEFT JOIN unit u ON j.UNIT = u.ID

	LEFT JOIN iki i ON p.ID = i.DINILAI

	LEFT JOIN kualitasiki kuali ON i.ID = kuali.IKI

	LEFT JOIN kualitas kual ON kuali.KUALITAS = kual.ID

	WHERE p.ID = $P{PEGAWAI} #AND i.ID LIKE '2019%'



UNION



SELECT u.UNIT,p.NIP,

	dbsdm.get_namaLengkap(p.ID) NAMA_A,

	j.JABATAN,

	p1.NIP NIP_ATL,

	dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,

	j1.JABATAN JABATAN_ATL,

	p2.NIP NIP_AT,

	dbsdm.get_namaLengkap(p2.ID) NAMA_AT,

	j2.JABATAN JABATAN_AT, i.IKI,

	per.INDIKATOR, per.DO DO, per.TARGET, peri.CAPAIAN, ROUND(per.BOBOT,3) BOBOT

	, ROUND((peri.CAPAIAN/per.TARGET)*per.BOBOT,3) NILAI, 'C' KET, 'PERILAKU' KET1, 'A2' KET2



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',12,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',1,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',2,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',3,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',4,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',5,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',6,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',7,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',8,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',9,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',10,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',11,'GAGAL')))))))))))) PERIODE_ANGKA



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))TAHUN_ANGKA







FROM pegawai1 p

	LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')

	LEFT JOIN jabatan j ON pj.JABATAN = j.ID

	LEFT JOIN jabatan j1 ON j.PARENT = j1.ID

	LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID

	LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')

	LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')

	LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID

	LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID

	LEFT JOIN unit u ON j.UNIT = u.ID

	LEFT JOIN iki i ON p.ID = i.DINILAI

	LEFT JOIN perilakuiki peri ON i.ID = peri.IKI

	LEFT JOIN perilaku per ON peri.PERILAKU = per.ID

WHERE p.ID = $P{PEGAWAI} #AND i.ID LIKE '2019%'



UNION



SELECT u.UNIT,p.NIP,

	dbsdm.get_namaLengkap(p.ID) NAMA_A,

	j.JABATAN,

	p1.NIP NIP_ATL,

	dbsdm.get_namaLengkap(p1.ID) NAMA_ATL,

	j1.JABATAN JABATAN_ATL,

	p2.NIP NIP_AT,

	dbsdm.get_namaLengkap(p2.ID) NAMA_AT,

	j2.JABATAN JABATAN_AT, i.IKI,

	kt.INDIKATOR, kt.RINCIAN DO, kt.TARGET, kti.CAPAIAN, ROUND(kti.BOBOT,3) BOBOT

	, ROUND((kti.CAPAIAN/kt.TARGET)*kti.BOBOT,3) NILAI, 'D' KET, 'PENGEMBANGAN PROFESI' KET1, 'A2' KET2



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT('DESEMBER ',SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT('JANUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT('FEBRUARI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT('MARET ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT('APRIL ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT('MEI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT('JUNI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT('JULI ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT('AGUSTUS ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT('SEPTEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT('OKTOBER ',SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT('NOVEMBER ',SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))	PERIODE



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',12,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',1,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',2,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',3,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',4,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',5,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',6,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',7,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',8,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',9,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',10,

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',11,'GAGAL')))))))))))) PERIODE_ANGKA



	, IF(SUBSTR(i.ID FROM 5 FOR 2)='01',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)-1),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='02',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='03',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='04',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='05',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='06',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='07',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='08',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='09',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='10',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='11',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),

	IF(SUBSTR(i.ID FROM 5 FOR 2)='12',CONCAT(SUBSTR(i.ID FROM 1 FOR 4)),'GAGAL'))))))))))))TAHUN_ANGKA







FROM pegawai1 p

	LEFT JOIN pejabat1 pj ON p.ID = pj.PEGAWAI AND pj.STATUS = '1' AND (pj.KATEGORI='1' OR pj.KATEGORI='3')

	LEFT JOIN jabatan j ON pj.JABATAN = j.ID

	LEFT JOIN jabatan j1 ON j.PARENT = j1.ID

	LEFT JOIN jabatan j2 ON j1.PARENT = j2.ID

	LEFT JOIN pejabat1 pj1 ON j1.ID = pj1.JABATAN AND pj1.STATUS = '1' AND (pj1.KATEGORI='1' OR pj1.KATEGORI='3')

	LEFT JOIN pejabat1 pj2 ON j2.ID = pj2.JABATAN AND pj2.STATUS = '1' AND (pj2.KATEGORI='1' OR pj2.KATEGORI='3')

	LEFT JOIN pegawai1 p1 ON pj1.PEGAWAI = p1.ID

	LEFT JOIN pegawai1 p2 ON pj2.PEGAWAI = p2.ID

	LEFT JOIN unit u ON j.UNIT = u.ID

	LEFT JOIN iki i ON p.ID = i.DINILAI

	LEFT JOIN kegiatan_tambahaniki kti ON i.ID = kti.IKI

	LEFT JOIN kegiatan_tambahan kt ON kti.KD_TAMBAHAN = kt.ID

WHERE p.ID =$P{PEGAWAI}  #AND i.ID LIKE '2019%'

) a



WHERE a.TAHUN_ANGKA=$P{TAHUN}



GROUP BY a.KET, a.INDIKATOR, a.DO]]>
	</queryString>
	<field name="UNIT" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NIP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NAMA_A" class="java.lang.String"/>
	<field name="JABATAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NIP_ATL" class="java.lang.String"/>
	<field name="NAMA_ATL" class="java.lang.String"/>
	<field name="JABATAN_ATL" class="java.lang.String"/>
	<field name="IKI" class="java.lang.Double">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="INDIKATOR" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="DO" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KET" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KET1" class="java.lang.String"/>
	<field name="KET2" class="java.lang.String"/>
	<field name="PERIODE" class="java.lang.String">
		<fieldDescription><![CDATA[periode jangka waktunya]]></fieldDescription>
	</field>
	<field name="TOTAL_TARGET" class="java.math.BigDecimal"/>
	<field name="TOTAL_CAPAIAN" class="java.lang.Double"/>
	<field name="BOBOT" class="java.lang.Double">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TOTAL_NILAI" class="java.lang.Double"/>
	<field name="CAPAIAN_JANUARI" class="java.lang.Double"/>
	<field name="NILAI_JANUARI" class="java.lang.Double"/>
	<field name="CAPAIAN_FEB" class="java.lang.Double"/>
	<field name="NILAI_FEB" class="java.lang.Double"/>
	<field name="CAPAIAN_MARET" class="java.lang.Double"/>
	<field name="NILAI_MARET" class="java.lang.Double"/>
	<field name="CAPAIAN_APRIL" class="java.lang.Double"/>
	<field name="NILAI_APRIL" class="java.lang.Double"/>
	<field name="CAPAIAN_MAY" class="java.lang.Double"/>
	<field name="NILAI_MAY" class="java.lang.Double"/>
	<field name="CAPAIAN_JUNE" class="java.lang.Double"/>
	<field name="NILAI_JUNE" class="java.lang.Double"/>
	<field name="CAPAIAN_JULY" class="java.lang.Double"/>
	<field name="NILAI_JULY" class="java.lang.Double"/>
	<field name="CAPAIAN_AUG" class="java.lang.Double"/>
	<field name="NILAI_AUG" class="java.lang.Double"/>
	<field name="CAPAIAN_SEPT" class="java.lang.Double"/>
	<field name="NILAI_SEPT" class="java.lang.Double"/>
	<field name="CAPAIAN_OCT" class="java.lang.Double"/>
	<field name="NILAI_OCT" class="java.lang.Double"/>
	<field name="CAPAIAN_NOV" class="java.lang.Double"/>
	<field name="NILAI_NOV" class="java.lang.Double"/>
	<field name="CAPAIAN_DES" class="java.lang.Double"/>
	<field name="NILAI_DES" class="java.lang.Double"/>
	<variable name="hit1" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{BOBOT}]]></variableExpression>
	</variable>
	<variable name="BOBOT_1" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{BOBOT}]]></variableExpression>
	</variable>
	<variable name="jum_1" class="java.lang.String">
		<variableExpression><![CDATA["JUMLAH " + $F{KET1}]]></variableExpression>
	</variable>
	<variable name="CapJan" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_JANUARI}]]></variableExpression>
	</variable>
	<variable name="NilJan" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_JANUARI}]]></variableExpression>
	</variable>
	<variable name="CapFeb" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_FEB}]]></variableExpression>
	</variable>
	<variable name="NilFeb" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_FEB}]]></variableExpression>
	</variable>
	<variable name="CapMrt" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_MARET}]]></variableExpression>
	</variable>
	<variable name="NilMrt" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_MARET}]]></variableExpression>
	</variable>
	<variable name="CapApr" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_APRIL}]]></variableExpression>
	</variable>
	<variable name="NilApril" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_APRIL}]]></variableExpression>
	</variable>
	<variable name="CapMei" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_MAY}]]></variableExpression>
	</variable>
	<variable name="NilMei" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_MAY}]]></variableExpression>
	</variable>
	<variable name="CapJun" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_JUNE}]]></variableExpression>
	</variable>
	<variable name="NilJun" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_JUNE}]]></variableExpression>
	</variable>
	<variable name="CapJul" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_JULY}]]></variableExpression>
	</variable>
	<variable name="NilJul" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_JULY}]]></variableExpression>
	</variable>
	<variable name="CapAgs" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_AUG}]]></variableExpression>
	</variable>
	<variable name="NilAgs" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_AUG}]]></variableExpression>
	</variable>
	<variable name="CapSep" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_SEPT}]]></variableExpression>
	</variable>
	<variable name="NilSep" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_SEPT}]]></variableExpression>
	</variable>
	<variable name="CapOkt" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_OCT}]]></variableExpression>
	</variable>
	<variable name="NilOkt" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_OCT}]]></variableExpression>
	</variable>
	<variable name="CapNov" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_NOV}]]></variableExpression>
	</variable>
	<variable name="NilNov" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_NOV}]]></variableExpression>
	</variable>
	<variable name="CapDes" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{CAPAIAN_DES}]]></variableExpression>
	</variable>
	<variable name="NilDes" class="java.lang.Double" resetType="Group" resetGroup="ket" calculation="Sum">
		<variableExpression><![CDATA[$F{NILAI_DES}]]></variableExpression>
	</variable>
	<group name="ket2">
		<groupExpression><![CDATA[$F{UNIT}]]></groupExpression>
		<groupFooter>
			<band height="160">
				<frame>
					<reportElement x="0" y="12" width="1466" height="148" uuid="028649b6-89ca-4b23-86cc-578420d04d0b"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textField isStretchWithOverflow="true">
						<reportElement stretchType="RelativeToTallestObject" x="1273" y="27" width="155" height="16" uuid="bd312b1d-edcf-4a5b-baa9-2c1fa2539006"/>
						<textElement>
							<font fontName="Calibri" size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{JABATAN_ATL}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="1273" y="7" width="122" height="20" uuid="d5e037b3-f2ae-4e12-8880-4eda01d647b4"/>
						<textElement verticalAlignment="Bottom">
							<font fontName="Calibri" size="7"/>
						</textElement>
						<text><![CDATA[Atasan Langsung]]></text>
					</staticText>
					<textField isStretchWithOverflow="true">
						<reportElement stretchType="RelativeToTallestObject" x="1273" y="95" width="175" height="20" uuid="4a17adb6-9f16-4d90-9a8e-31dd57a1e80c"/>
						<textElement verticalAlignment="Bottom">
							<font fontName="Calibri" size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{NAMA_ATL}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="1273" y="115" width="16" height="20" uuid="31baba3e-7aa2-4a72-9340-b401e8d5769c"/>
						<textElement verticalAlignment="Top">
							<font fontName="Calibri" size="7"/>
						</textElement>
						<text><![CDATA[NIP :]]></text>
					</staticText>
					<textField isBlankWhenNull="true">
						<reportElement x="1289" y="115" width="112" height="20" uuid="b1177bd1-a662-4e2d-b6a6-81fe1d1d6897"/>
						<textElement verticalAlignment="Top">
							<font fontName="Calibri" size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{NIP_ATL}]]></textFieldExpression>
					</textField>
				</frame>
			</band>
		</groupFooter>
	</group>
	<group name="ket1">
		<groupExpression><![CDATA[$F{KET2}]]></groupExpression>
		<groupFooter>
			<band height="16">
				<textField>
					<reportElement x="0" y="0" width="466" height="16" uuid="ef4efb67-a750-419f-96fd-5a1894eb0d10"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET2}.equals( 'A1' ) ? 'JUMLAH KUANTITAS DAN KUALITAS' : 'TOTAL NILAI KINERJA INDIVIDU']]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="466" y="0" width="40" height="16" uuid="f039afa7-18e7-4bcf-8c58-273b0cfc7a69"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{hit1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="ket">
		<groupExpression><![CDATA[$F{KET}]]></groupExpression>
		<groupHeader>
			<band height="10">
				<textField>
					<reportElement mode="Opaque" x="0" y="0" width="33" height="10" backcolor="#FFCC99" uuid="738387a8-808d-4aca-a1ea-f0e32cc789ce"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="33" y="0" width="1433" height="10" backcolor="#FFCC99" uuid="a903d139-b25e-4035-9335-2881bb90a8ca"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET1}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<rectangle>
					<reportElement x="0" y="0" width="33" height="17" uuid="a94f1155-e098-4ee7-939c-782769bd40ac"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="466" y="0" width="40" height="17" uuid="8375e412-726a-45fa-89d6-0f1edc5c4203"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BOBOT_1}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="33" y="0" width="433" height="17" uuid="067d99a4-5790-48c2-bb1b-c1cb1add4e6e"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
						<paragraph leftIndent="5"/>
					</textElement>
					<text><![CDATA[JUMLAH]]></text>
				</staticText>
				<textField>
					<reportElement x="76" y="0" width="104" height="17" uuid="d79dd36e-9d49-4f6a-9632-e44dbfea0d91"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KET1}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="506" y="0" width="40" height="17" uuid="e8adf8ad-3f27-4634-9c6e-df21b673036c"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapJan}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="546" y="0" width="40" height="17" uuid="1a29c19c-14bc-4952-97ba-06c9d589b940"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilJan}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="586" y="0" width="40" height="17" uuid="a1e445bc-ca55-4217-b783-8f996d380e8d"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapFeb}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="626" y="0" width="40" height="17" uuid="18f7d464-0912-4f8b-a6e5-2687ed6ed1ce"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilFeb}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="666" y="0" width="40" height="17" uuid="e9988b5f-1b39-4fe8-b029-be1d4f286b17"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapMrt}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="706" y="0" width="40" height="17" uuid="a0058d4a-ab32-43b8-b83d-9d80a7d17674"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilMrt}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="746" y="0" width="40" height="17" uuid="0906a20e-4fd8-47f6-b1d5-b81d25725eb9"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapApr}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="786" y="0" width="40" height="17" uuid="bc4a7306-0b06-44b6-b949-14967bda71ce"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilApril}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="826" y="0" width="40" height="17" uuid="09484eea-63fc-4c84-bafb-74f8ca3db930"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapMei}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="866" y="0" width="40" height="17" uuid="c543fd03-d548-4766-a13e-f708f917cba1"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilMei}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="906" y="0" width="40" height="17" uuid="ba7b3d7e-ab7b-4aa4-84ef-0c2ea4425409"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapJun}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="946" y="0" width="40" height="17" uuid="d7368296-b744-4d13-891b-7c1963762d5c"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilJun}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="986" y="0" width="40" height="17" uuid="dd629869-e8c1-4127-89e9-7a403f0116b9"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapJul}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="1026" y="0" width="40" height="17" uuid="87773281-bdf8-4a39-9f77-599b1d3adc31"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilJul}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="1066" y="0" width="40" height="17" uuid="ceb8e15c-68ab-49b4-9934-ef0b7d0fec28"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapAgs}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="1106" y="0" width="40" height="17" uuid="f964f73c-5436-4ab9-8c23-a261c1f466c3"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilAgs}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="1146" y="0" width="40" height="17" uuid="bf858a8a-e654-4413-8325-aa633f464e08"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapSep}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="1186" y="0" width="40" height="17" uuid="52003b6f-f4bf-403c-9eb3-c7c342fd8b2c"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilSep}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="1266" y="0" width="40" height="17" uuid="9d245b7a-2aaf-45bd-b500-8aa59a09e02f"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilOkt}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="1226" y="0" width="40" height="17" uuid="b424e57f-d27d-44d2-95bf-d89ba479d17c"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapOkt}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="1306" y="0" width="40" height="17" uuid="5925d94a-74a8-4297-9f43-e7e55c03ad0e"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapNov}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="1346" y="0" width="40" height="17" uuid="3edd6627-03f3-4846-bc26-c7f39e2b6909"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilNov}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.000" isBlankWhenNull="true">
					<reportElement x="1426" y="0" width="40" height="17" uuid="68e63aed-caba-4b51-9dd0-f9afa50feac3"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NilDes}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement x="1386" y="0" width="40" height="17" uuid="47eeed2a-a089-410f-a135-1c628548cd7a"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{CapDes}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="153">
			<staticText>
				<reportElement x="0" y="72" width="1448" height="18" uuid="54c2021b-4117-4986-b655-47e673fd4351"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[PENILAIAN KINERJA]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="91" width="64" height="10" uuid="50f80c44-7ca6-48cc-9f0a-b903dd4379e7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NAMA]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="101" width="64" height="10" uuid="d72ae43e-3552-4397-a5b7-b245f6f24461"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NIP]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="111" width="64" height="10" uuid="6e7256d9-8e75-48ed-953f-eb5c07494104"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[JABATAN]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="121" width="64" height="10" uuid="19ed6147-5447-4f20-bd90-ee3bd12252a0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[UNIT KERJA]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="131" width="64" height="10" uuid="0a8b8b4b-f0ea-45b8-a8e4-8992a83fb80e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[PERIODE]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="91" width="14" height="10" uuid="5970554d-039d-4679-aa03-d58e3b30e4fb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="101" width="14" height="10" uuid="832cdd7b-c440-4ec9-ad59-9f5015a68f6a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="111" width="14" height="10" uuid="aa2d23ec-0343-4ae0-8500-7997c45be07b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="121" width="14" height="10" uuid="2fad4568-6a35-46cd-9937-2a2105f4e364"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="131" width="14" height="10" uuid="dd022b2a-c4f3-49d6-aa2c-fd1e6b7a3ece"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="91" width="279" height="10" uuid="061d1aa8-18dc-4925-82db-25d6110a19bd"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NAMA_A}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="101" width="279" height="10" uuid="7debfb61-c418-4f0c-904b-00c523297dc8"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NIP}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="111" width="279" height="10" uuid="643e4a25-0588-4f72-9554-9c79dcab2453"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{JABATAN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="121" width="279" height="10" uuid="2f59505c-5b7b-487b-a105-5f7e4fc94bbe"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{UNIT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="187" y="131" width="279" height="10" uuid="9c76537b-cd3c-48ee-8411-ed2d0e895ef6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PERIODE}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="1393" y="14" width="41" height="47" uuid="3df54cae-dc9b-4f14-bc8c-e420ea3e1e76"/>
				<imageExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\laporan\\logo-dharmais.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="76" y="14" width="47" height="33" uuid="bbe73d45-82bc-4d6d-82a8-d49fe953b5be"/>
				<imageExpression><![CDATA["C:\\xampp\\htdocs\\simpeg\\laporan\\logo-kemenkes.png"]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="0" y="5" width="1448" height="25" uuid="ee0269c0-47bc-4358-8079-f8b94d711df0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[RUMAH SAKIT KANKER "DHARMAIS"]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="30" width="1393" height="17" uuid="8f63c49a-d629-49de-ab19-271a2142ca00"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Jl. Let. Jend. S. Parman Kav. 84-86, Slipi, Jakarta Barat 11420]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="46" width="1448" height="17" uuid="2beb8e18-4fec-424e-91e1-291a52b0d4c7"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Telepon : (021) 5681570 Faximile : (021) 5681579]]></text>
			</staticText>
			<line>
				<reportElement x="5" y="72" width="1443" height="1" uuid="568e5671-eb0a-4884-8e13-e61477cc8b7d"/>
				<graphicElement>
					<pen lineStyle="Double"/>
				</graphicElement>
			</line>
		</band>
	</title>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="0" y="12" width="33" height="9" backcolor="#FFFF99" uuid="bcc674bb-118e-4b64-bef7-dcab521e64a6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[1]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="33" height="12" backcolor="#FFFF99" uuid="ad0d4fc2-9ab6-4a4e-b685-ddba1ec9e8a1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NO	]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="33" y="12" width="154" height="9" backcolor="#FFFF99" uuid="70a8a398-0d98-4dbe-8a18-b39c556dc532"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[2]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="33" y="0" width="154" height="12" backcolor="#FFFF99" uuid="2c34f8fe-2237-4b70-832b-f1a643e00f0e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[KINERJA]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="187" y="12" width="164" height="9" backcolor="#FFFF99" uuid="360f6b02-2284-43c3-8200-097d0094dc4a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[3]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="351" y="12" width="57" height="9" backcolor="#FFFF99" uuid="3654b5e6-bbb6-4d62-89be-2bfeae2a4bff"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[4]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="408" y="0" width="58" height="12" backcolor="#FFFF99" uuid="d670437c-149a-4eb6-ad4d-20a628917d5a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[CAPAIAN]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="408" y="12" width="58" height="9" backcolor="#FFFF99" uuid="0e98a9c4-12d9-478b-a862-f00f84797630"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[5]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="466" y="12" width="40" height="9" backcolor="#FFFF99" uuid="3fcacf2d-1ce1-435c-aedd-6058cbc1cae9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[6]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="466" y="0" width="40" height="12" backcolor="#FFFF99" uuid="79c2cb1e-ebb1-405c-a190-73d730ea2f08"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[BOBOT %]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="187" y="0" width="164" height="12" backcolor="#FFFF99" uuid="80628627-d726-408b-994d-70c7eac78e15"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[INDIKATOR KINERJA	]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="351" y="0" width="57" height="12" backcolor="#FFFF99" uuid="705b6e90-d25e-4dbd-b3cb-4911cc61588a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[TARGET]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="506" y="0" width="80" height="12" backcolor="#FFFF99" uuid="04cbb259-a725-4335-8739-8ad3114b22d0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Januari]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="506" y="12" width="40" height="9" backcolor="#FFFF99" uuid="10358eea-ff39-4007-9ffc-fb34514084a1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="546" y="12" width="40" height="9" backcolor="#FFFF99" uuid="717683fe-2748-490f-afe4-30f398e903a8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="586" y="0" width="80" height="12" backcolor="#FFFF99" uuid="3cb68995-53c2-41d4-bd8a-ed4465cfffde"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Februari]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="586" y="12" width="40" height="9" backcolor="#FFFF99" uuid="ceefe256-8d1c-48b0-bd3d-4df6d04491bf"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="626" y="12" width="40" height="9" backcolor="#FFFF99" uuid="6451f9ae-6167-4308-b7b4-12ea047eb568"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="666" y="0" width="80" height="12" backcolor="#FFFF99" uuid="6d06d479-c06e-430a-9be6-bcb4be5719a9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Maret]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="706" y="12" width="40" height="9" backcolor="#FFFF99" uuid="4b10ec82-7728-4133-aba8-063c2e12ce9a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="666" y="12" width="40" height="9" backcolor="#FFFF99" uuid="687d0496-5b91-4570-9989-4a624b61207e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="746" y="0" width="80" height="12" backcolor="#FFFF99" uuid="4f192991-02a2-4e73-9f1f-28f65feae5c2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[April]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="786" y="12" width="40" height="9" backcolor="#FFFF99" uuid="db01a7e5-017b-486f-879c-8a4e7621152a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="746" y="12" width="40" height="9" backcolor="#FFFF99" uuid="25428bcc-9f7e-4d0f-8268-6e697bd56db8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="826" y="0" width="80" height="12" backcolor="#FFFF99" uuid="85fd43b1-519b-4b32-890a-e9b3af25208b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Mei]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="826" y="12" width="40" height="9" backcolor="#FFFF99" uuid="01319046-5f50-43c4-bc0b-9a086315446c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="866" y="12" width="40" height="9" backcolor="#FFFF99" uuid="707d4f6a-44b5-4b7a-a9b3-95558cd5024f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="906" y="12" width="40" height="9" backcolor="#FFFF99" uuid="c42578cd-8a61-4536-b38e-952e1483fead"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="946" y="12" width="40" height="9" backcolor="#FFFF99" uuid="ab93f11b-b75e-420c-8ab4-764b26fc721b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="906" y="0" width="80" height="12" backcolor="#FFFF99" uuid="3d120e7b-2f53-431d-a218-cf14196aa225"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Juni]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="986" y="0" width="80" height="12" backcolor="#FFFF99" uuid="c55322da-fb15-4d4e-aa01-d26f4fbe306c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Juli]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1066" y="0" width="80" height="12" backcolor="#FFFF99" uuid="224c9f79-de56-4bbe-9538-27534e3d397b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Agustus]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1146" y="0" width="80" height="12" backcolor="#FFFF99" uuid="05588744-718a-46ed-a2f9-ebe82e79e1b5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[September]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1226" y="0" width="80" height="12" backcolor="#FFFF99" uuid="78e53f54-2e7f-4ff4-af88-a62d73d80e01"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Oktober]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1306" y="0" width="80" height="12" backcolor="#FFFF99" uuid="2c4b53a5-44a6-4b42-b416-9684d865dc10"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[November]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1386" y="0" width="80" height="12" backcolor="#FFFF99" uuid="0ab8d26c-d62d-437d-8f52-e19d93457fd5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Desember]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="986" y="12" width="40" height="9" backcolor="#FFFF99" uuid="4ecb8469-008d-46e6-949a-77b328ba283e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1026" y="12" width="40" height="9" backcolor="#FFFF99" uuid="3e8a9764-643c-4655-ad1d-f88fdee30008"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1106" y="12" width="40" height="9" backcolor="#FFFF99" uuid="299ac547-c6f7-4e1e-92d2-e36c611541be"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1066" y="12" width="40" height="9" backcolor="#FFFF99" uuid="526ee629-5fa6-4985-b2c6-c1ed96e468c8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1186" y="12" width="40" height="9" backcolor="#FFFF99" uuid="a32e8439-afdb-4ee5-a449-eec613ff540d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1146" y="12" width="40" height="9" backcolor="#FFFF99" uuid="4460c7b0-4f66-4129-8a70-c4338b08a087"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1266" y="12" width="40" height="9" backcolor="#FFFF99" uuid="4783486d-4cd4-4db7-a63e-ff77d7bcb317"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1226" y="12" width="40" height="9" backcolor="#FFFF99" uuid="16c27d5e-3b1d-437c-bdb1-498ddaf4f4fe"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1346" y="12" width="40" height="9" backcolor="#FFFF99" uuid="02f51b52-ff38-47b7-8fa5-4c1734b669d6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1306" y="12" width="40" height="9" backcolor="#FFFF99" uuid="3df38225-5363-452e-85f7-d7cb37c501c6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1426" y="12" width="40" height="9" backcolor="#FFFF99" uuid="d68578a6-55b2-4dde-b854-a41b457824d5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nilai]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1386" y="12" width="40" height="9" backcolor="#FFFF99" uuid="814d212a-9d02-40ee-bcf2-1562a5b375ef"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Capaian]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="51" y="0" width="136" height="14" uuid="1e1f0d5f-af14-4fdf-814c-940eeb785615"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="20"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{INDIKATOR}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="187" y="0" width="164" height="14" uuid="2fe0c531-a715-4dd4-a402-fbb685cc248d"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="20"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="408" y="0" width="58" height="14" uuid="856dd53f-882c-44c6-950d-5cc04469d8af"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOTAL_CAPAIAN}.intValue()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="466" y="0" width="40" height="14" uuid="3b539ace-920f-4ba9-b077-82c300f5e43f"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BOBOT}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="33" height="14" uuid="6497cfa3-ca40-44ba-b2db-f9ff020bf46f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="33" y="0" width="18" height="14" uuid="d05c7994-c515-4eff-b293-e70003234986"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isStrikeThrough="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{ket_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="351" y="0" width="57" height="14" uuid="16faebd3-5255-49a7-b06d-d2bb7e32cd84"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOTAL_TARGET}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="506" y="0" width="40" height="14" uuid="ee35f20d-c51a-43a5-92ae-19fdb9c3a35d"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_JANUARI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.000" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="546" y="0" width="40" height="14" uuid="62aa7728-f03d-4686-a5e1-c9b295893761"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_JANUARI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="586" y="0" width="40" height="14" uuid="3ab60e0b-ba03-4021-b668-b175cad43b21"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_FEB}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="626" y="0" width="40" height="14" uuid="5621d0a1-03c6-4416-8b65-d4bc4b6e9e1e"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_FEB}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="706" y="0" width="40" height="14" uuid="7a96bc82-4329-46a7-bbf1-e1238e14894c"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_MARET}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="666" y="0" width="40" height="14" uuid="109428c3-f31b-4b73-9e1e-bba920b023f1"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_MARET}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="746" y="0" width="40" height="14" uuid="a2cd164f-7839-4505-b7c0-8dd26e3774aa"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_APRIL}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="786" y="0" width="40" height="14" uuid="1243a971-ba1f-4f73-ba88-a290e1e522e1"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_APRIL}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="866" y="0" width="40" height="14" uuid="822b39c8-abaf-4c28-b631-476da9db03da"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_MAY}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="826" y="0" width="40" height="14" uuid="7a29cb5b-f464-4873-b797-a3313d29e1d0"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_MAY}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="906" y="0" width="40" height="14" uuid="acf46064-ed0b-430c-8add-83706d056a62"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_JUNE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="946" y="0" width="40" height="14" uuid="f740dd79-d1cb-45ff-bf9c-418f344690b1"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_JUNE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="986" y="0" width="40" height="14" uuid="ac4e9a39-32ac-4684-a0e3-27a7d2356be4"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_JULY}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1026" y="0" width="40" height="14" uuid="a56b4f8f-d244-40c2-83d1-392e0c8d2152"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_JULY}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1066" y="0" width="40" height="14" uuid="11d3b51f-9c51-4f6c-b537-f5370c131547"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_AUG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1106" y="0" width="40" height="14" uuid="1b560972-42d7-45e2-a37d-c7f4280d6be8"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_AUG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1146" y="0" width="40" height="14" uuid="f1a5e313-8176-4693-a566-446322ed3701"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_SEPT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1186" y="0" width="40" height="14" uuid="2c49e3e4-c46f-4613-831b-4cd7c80cf089"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_SEPT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1226" y="0" width="40" height="14" uuid="3ba08a91-c63a-48e5-b3dc-668aca78a646"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_OCT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1266" y="0" width="40" height="14" uuid="ac48d664-b4ca-41c4-a8e8-c1f189016253"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_OCT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1306" y="0" width="40" height="14" uuid="4d86079b-90e3-4924-84c5-c797521dde67"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_NOV}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1346" y="0" width="40" height="14" uuid="cb312a6e-44d0-4afe-9b61-0a1c537acdb8"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_NOV}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1386" y="0" width="40" height="14" uuid="4256854f-bc0c-4f13-a7da-bbcd51f5e2dc"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPAIAN_DES}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1426" y="0" width="40" height="14" uuid="01338134-7cc4-4fdd-950b-8e010edeec48"/>
				<box topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7"/>
					<paragraph lineSpacing="Single" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NILAI_DES}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
