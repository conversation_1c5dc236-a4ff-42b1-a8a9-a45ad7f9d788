<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="pendapatanUnit" language="groovy" pageWidth="1157" pageHeight="595" orientation="Landscape" columnWidth="1117" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="de220342-1dc4-48ac-beb8-4654b11d750d">
	<property name="ireport.zoom" value="0.9075000000000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="TAHUN" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="BULAN" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT u.ID, u.UNIT, DATE_FORMAT(r.BULAN_TAHUN, '%M %Y'), DATE_FORMAT(CURDATE(), '%d/%m/%Y') AS TANGGAL
-- , (SELECT * FROM dbsdm.pegawai1 p
-- 	WHERE p.STATUS_AKTIF = 1 AND pg.ID = GROUP BY )
, SUM(IF(pg.STATUS_AKTIF = 1 AND pg.STATUS_PEGAWAI IN (9,12,10,1037,1033), g.GAJIBERSIH, 0)) TOTAL_GAJI
, SUM(IF(pg.STATUS_AKTIF = 1 AND pg.STATUS_PEGAWAI IN (9,12), g.GAJIBERSIH, 0)) TOTAL_GAJI_PNS
, SUM(IF(pg.STATUS_AKTIF = 1 AND pg.STATUS_PEGAWAI IN (10,1037), g.GAJIBERSIH, 0)) TOTAL_GAJI_BLU
, SUM(IF(pg.STATUS_AKTIF = 1 AND pg.STATUS_PEGAWAI = 1033, g.GAJIBERSIH, 0)) TOTAL_GAJI_PPPK

, SUM(IF(pg.STATUS_AKTIF = 1 AND pg.STATUS_PEGAWAI IN (9,12,10,1037,1033), r.JML_REMUN_DIBAYARKAN, 0)) TOTAL_REMUN
, SUM(IF(pg.STATUS_AKTIF = 1 AND pg.STATUS_PEGAWAI IN (9,12), r.JML_REMUN_DIBAYARKAN, 0)) TOTAL_REMUN_PNS
, SUM(IF(pg.STATUS_AKTIF = 1 AND pg.STATUS_PEGAWAI IN (10,1037), r.JML_REMUN_DIBAYARKAN, 0)) TOTAL_REMUN_BLU
, SUM(IF(pg.STATUS_AKTIF = 1 AND pg.STATUS_PEGAWAI = 1033, r.JML_REMUN_DIBAYARKAN, 0)) TOTAL_REMUN_PPPK

FROM dbsdm.pegawai1 pg

LEFT JOIN dbsdm.pejabat1 pj ON pj.PEGAWAI = pg.ID #AND pj.STATUS = 1
LEFT JOIN dbsdm.jabatan j ON j.ID = pj.JABATAN #AND j.STATUS = 1
LEFT JOIN dbsdm.unit u ON u.ID = j.UNIT
LEFT JOIN pendapatan.gaji g ON g.NOABSEN = pg.ABSEN AND g.BULAN = $P{BULAN}
LEFT JOIN pendapatan.tb_slip_remun r ON r.ABSEN = pg.ABSEN AND MONTH(r.BULAN_TAHUN) = $P{BULAN}

WHERE #u.ID = 5005
#AND
pg.STATUS_AKTIF = 1
AND
pg.STATUS_PEGAWAI IN (9,10,11,12,1033,1037)
AND
pj.STATUS = 1 AND j.STATUS = 1 AND u.STATUS = 1
AND YEAR(r.BULAN_TAHUN) = $P{TAHUN}


GROUP BY u.ID]]>
	</queryString>
	<field name="ID" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="UNIT" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="DATE_FORMAT(r.BULAN_TAHUN, &apos;%M %Y&apos;)" class="java.lang.String"/>
	<field name="TANGGAL" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TOTAL_GAJI" class="java.math.BigDecimal"/>
	<field name="TOTAL_GAJI_PNS" class="java.math.BigDecimal"/>
	<field name="TOTAL_GAJI_BLU" class="java.math.BigDecimal"/>
	<field name="TOTAL_GAJI_PPPK" class="java.math.BigDecimal"/>
	<field name="TOTAL_REMUN" class="java.math.BigDecimal"/>
	<field name="TOTAL_REMUN_PNS" class="java.math.BigDecimal"/>
	<field name="TOTAL_REMUN_BLU" class="java.math.BigDecimal"/>
	<field name="TOTAL_REMUN_PPPK" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="142" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="1114" height="20" uuid="318bbc2f-9a84-4906-bb57-4090495f5f1f"/>
				<textElement textAlignment="Center"/>
				<text><![CDATA[LAPORAN PENDAPATAN PER UNIT]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="20" width="1114" height="20" uuid="7cc1bfd0-aaba-4f95-95e7-fe39a3b2c2a9"/>
				<textElement textAlignment="Center"/>
				<text><![CDATA[RUMAH SAKIT KANKER "DHARMAIS"]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="40" width="1114" height="20" uuid="4f14429c-f333-45f8-874f-13021e42a77c"/>
				<textElement textAlignment="Center"/>
				<text><![CDATA[Jl. Let. Jend S. Parman Kav. 84 - 86, Slipi, Jakarta Barat 11420]]></text>
			</staticText>
			<staticText>
				<reportElement x="25" y="122" width="278" height="20" uuid="0fa1e8c2-e33c-488e-afe4-379ad42375cb"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[UNIT]]></text>
			</staticText>
			<staticText>
				<reportElement x="303" y="122" width="100" height="20" uuid="01535265-958c-4e87-b92e-789832f14d64"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[TOTAL GAJI]]></text>
			</staticText>
			<staticText>
				<reportElement x="403" y="122" width="100" height="20" uuid="38042911-c809-4291-866b-b7d15d29e094"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[TOTAL GAJI PNS]]></text>
			</staticText>
			<staticText>
				<reportElement x="503" y="122" width="100" height="20" uuid="59282cd7-660b-425d-885e-073f32b8632f"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[TOTAL GAJI BLU]]></text>
			</staticText>
			<staticText>
				<reportElement x="603" y="122" width="100" height="20" uuid="ec0171e1-6373-4e23-aec6-130182444599"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[TOTAL GAJI PPPK]]></text>
			</staticText>
			<staticText>
				<reportElement x="703" y="122" width="100" height="20" uuid="c991d641-2fe9-4d31-a231-b51ac3792f92"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[TOTAL REMUN]]></text>
			</staticText>
			<staticText>
				<reportElement x="803" y="122" width="100" height="20" uuid="412288b6-725c-4ad7-b52c-8d2e62f3db90"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[TOTAL REMUN PNS]]></text>
			</staticText>
			<staticText>
				<reportElement x="903" y="122" width="100" height="20" uuid="0e950dec-c9ac-44f2-98ae-f0de49718891"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[TOTAL REMUN BLU]]></text>
			</staticText>
			<staticText>
				<reportElement x="1003" y="122" width="111" height="20" uuid="a7b3a180-984d-4493-8f5d-bdc001f6e089"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[TOTAL REMUN PPPK]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="122" width="25" height="20" uuid="9481f1ad-4f5f-436c-8f66-a1604c9dde4f"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[NO.]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="82" width="101" height="20" uuid="34a1e790-9a3a-4651-9fd9-c22bd0375103"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<text><![CDATA[Periode :]]></text>
			</staticText>
			<textField>
				<reportElement x="101" y="82" width="100" height="20" uuid="5b2ad290-7655-42a7-8bba-fa8f92a9ba12"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{DATE_FORMAT(r.BULAN_TAHUN, '%M %Y')}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="25" y="0" width="278" height="20" uuid="6550f486-88ad-4d8e-bf3e-2fd2c47617ee"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{UNIT}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00">
				<reportElement x="303" y="0" width="100" height="20" uuid="118f4b44-c6dd-4df5-bc5c-1fd363b70dd2"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TOTAL_GAJI} == null ? "-" : $F{TOTAL_GAJI}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00">
				<reportElement x="403" y="0" width="100" height="20" uuid="dbf7463e-d185-49af-bfa3-c8eaf5405e97"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TOTAL_GAJI_PNS} == null ? "-" : $F{TOTAL_GAJI_PNS}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00">
				<reportElement x="503" y="0" width="100" height="20" uuid="175f3bf6-097c-44ac-816e-d519303b5e59"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TOTAL_GAJI_BLU} == null ? "-" : $F{TOTAL_GAJI_BLU}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00">
				<reportElement x="603" y="0" width="100" height="20" uuid="77a58be2-b7c7-4e28-b5ae-36645eda4f87"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TOTAL_GAJI_PPPK} == null ? "-" : $F{TOTAL_GAJI_PPPK}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00">
				<reportElement x="703" y="0" width="100" height="20" uuid="d3f35fc0-bcad-4ff7-968e-6c6337d3fb22"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TOTAL_REMUN} == null ? "-" : $F{TOTAL_REMUN}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00">
				<reportElement x="803" y="0" width="100" height="20" uuid="0cbdcf58-4cf2-4058-bb0b-0f0495f9bed7"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TOTAL_REMUN_PNS} == null ? "-" : $F{TOTAL_REMUN_PNS}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00">
				<reportElement x="903" y="0" width="100" height="20" uuid="ced6eea3-0f8e-4923-ae71-1137bef7e7a4"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TOTAL_REMUN_BLU} == null ? "-" : $F{TOTAL_REMUN_BLU}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00">
				<reportElement x="1003" y="0" width="111" height="20" uuid="3d1221d3-0777-45c7-9baf-6a22185ec48f"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TOTAL_REMUN_PPPK} == null ? "-" : $F{TOTAL_REMUN_PPPK}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="25" height="20" uuid="ec846f3e-c501-4410-a38d-032becc4ff6e"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="54" splitType="Stretch">
			<textField>
				<reportElement x="1018" y="34" width="96" height="20" uuid="4be955b3-5662-4f2f-8a85-c20e4abe6b60"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{TANGGAL}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="917" y="34" width="101" height="20" uuid="249595be-9f0e-4e75-96a4-7139e2b7b042"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<text><![CDATA[Tanggal Cetak :]]></text>
			</staticText>
		</band>
	</columnFooter>
	<pageFooter>
		<band height="54" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
