<!doctype html>
<html>
<head>
<title>LINECONS - 48 Fully Scalable Vector Icons</title>
<link rel="stylesheet" href="style.css" />
<link href='http://fonts.googleapis.com/css?family=Varela+Round' rel='stylesheet' type='text/css'>
<!--[if lte IE 7]><script src="lte-ie7.js"></script><![endif]-->

<!-- 

Thanks http://icomoon.io/app/ for amazing service!

-->


<style>
	section, header, footer {display: block;}
	body {
		font-family: 'Varela Round', sans-serif;
		color: #333333;
		line-height: 1.5;
		font-size: 16px;
		background:#26ae5f;
		color:white;
		-moz-text-shadow: 0 1px 1px rgba(0, 0, 0, 0.21);
		-webkit-text-shadow: 0 1px 1px rgba(0, 0, 0, 0.21);
	}
	* {
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		margin: 0;
		padding: 0;
	}
	.glyph {
		float: left;
		text-align: center;
		padding: .5em;
		margin: 0 .5em;
		width: 7em;
		border-radius: .375em;
		top:0;
		position:relative;
		-webkit-transition:0.2s ease-in;
		-o-transition:0.2s ease-in;
		-moz-transition:0.2s ease-in;
		-ie-transition:0.2s ease-in;
		transition:0.2s ease-in;
	}
	.glyph input {
		width: 100%;
		text-align: center;
		font-family: consolas, monospace;
		border-radius:3px;
		border:none;
		padding:4px;
		background:#26AE5C;
		color:#26AE5C;
		-webkit-transition:0.2s ease-in;
		-o-transition:0.2s ease-in;
		-moz-transition:0.2s ease-in;
		-ie-transition:0.2s ease-in;
		transition:0.2s ease-in;
	}
	.glyph:hover input {
		background:#26ae5f;
		padding:4px;
		color:#ffffff;
	}
	.glyph:hover {
		-webkit-transition: all 0.5s ease-out;
		background:#222821;
		color:#fff;
		text-shadow: none;
		-moz-text-shadow: none;
		-webkit-text-shadow: none;
		top:-5px;
		position:relative;
	}
	.glyph input, .mtm {
		margin-top: .75em;
	}
	.w-main {
		width: 770px;
		text-align:center;
	}
	.centered {
		margin-left: auto;
		margin-right: auto;
	}
	.fs1 {
		font-size: 3em;
	}
	header {
		margin: 2em 0;
		padding-bottom: .5em;
	}
	header h1 {
		font-size: 3.5em;
		font-weight: normal;
	}
	.clearfix:before, .clearfix:after { content: ""; display: table; }
	.clearfix:after, .clear { clear: both; }
	footer {
		margin: 2em 0;
		padding: 2em 0;
		border-top: 1px solid rgb(60, 189, 114);
		box-shadow: 0 -1px rgb(33, 165, 89);
	}
	a, a:visited {
		color: #ffffff; text-decoration:none;
	}
	a:hover, a:focus {color: #222821; text-decoration:none;}
	.box1 {
		display: inline-block;
		width: 14em;
		padding: .25em .5em;
		margin: .5em 1em .5em 0;
		text-align:left;
		
	}
	.box1 span { 
		font-size:2em;
		position: relative;
		top: 0.25em;
		margin-right: 0.1em;
	}
</style>
</head>
<body>
	<div class="w-main centered">
	<section class="mtm clearfix" id="glyphs">
	<header>
		<h1>LINECONS</h1>
		<p>48 FULLY SCALABLE VECTOR ICONS</p>
	</header>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe000;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe000;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe001;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe001;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe002;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe002;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe003;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe003;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe004;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe004;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe005;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe005;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe006;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe006;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe007;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe007;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe008;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe008;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe009;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe009;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00a;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00a;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00b;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00b;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00c;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00c;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00d;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00d;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00e;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00e;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00f;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00f;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe010;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe010;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe011;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe011;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe012;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe012;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe013;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe013;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe014;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe014;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe015;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe015;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe016;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe016;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe017;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe017;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe018;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe018;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe019;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe019;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe01a;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe01a;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe01b;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe01b;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe01c;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe01c;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe01d;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe01d;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe01e;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe01e;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe01f;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe01f;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe020;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe020;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe021;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe021;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe022;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe022;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe023;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe023;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe024;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe024;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe025;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe025;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe026;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe026;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe027;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe027;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe028;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe028;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe029;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe029;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe02a;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe02a;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe02b;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe02b;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe02c;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe02c;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe02d;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe02d;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe02e;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe02e;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe02f;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe02f;" />
	</div>
	</section>
	<div class="clear"></div>
	<section class="mtm clearfix" id="glyphs">
	<header>
		<h1>CSS Class Names</h1>
	</header>
	<span class="box1">
		<span aria-hidden="true" class="li_heart"></span>
		&nbsp;li_heart
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_cloud"></span>
		&nbsp;li_cloud
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_star"></span>
		&nbsp;li_star
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_tv"></span>
		&nbsp;li_tv
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_sound"></span>
		&nbsp;li_sound
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_video"></span>
		&nbsp;li_video
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_trash"></span>
		&nbsp;li_trash
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_user"></span>
		&nbsp;li_user
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_key"></span>
		&nbsp;li_key
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_search"></span>
		&nbsp;li_search
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_settings"></span>
		&nbsp;li_settings
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_camera"></span>
		&nbsp;li_camera
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_tag"></span>
		&nbsp;li_tag
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_lock"></span>
		&nbsp;li_lock
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_bulb"></span>
		&nbsp;li_bulb
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_pen"></span>
		&nbsp;li_pen
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_diamond"></span>
		&nbsp;li_diamond
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_display"></span>
		&nbsp;li_display
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_location"></span>
		&nbsp;li_location
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_eye"></span>
		&nbsp;li_eye
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_bubble"></span>
		&nbsp;li_bubble
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_stack"></span>
		&nbsp;li_stack
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_cup"></span>
		&nbsp;li_cup
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_phone"></span>
		&nbsp;li_phone
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_news"></span>
		&nbsp;li_news
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_mail"></span>
		&nbsp;li_mail
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_like"></span>
		&nbsp;li_like
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_photo"></span>
		&nbsp;li_photo
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_note"></span>
		&nbsp;li_note
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_clock"></span>
		&nbsp;li_clock
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_paperplane"></span>
		&nbsp;li_paperplane
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_params"></span>
		&nbsp;li_params
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_banknote"></span>
		&nbsp;li_banknote
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_data"></span>
		&nbsp;li_data
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_music"></span>
		&nbsp;li_music
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_megaphone"></span>
		&nbsp;li_megaphone
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_study"></span>
		&nbsp;li_study
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_lab"></span>
		&nbsp;li_lab
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_food"></span>
		&nbsp;li_food
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_t-shirt"></span>
		&nbsp;li_t-shirt
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_fire"></span>
		&nbsp;li_fire
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_clip"></span>
		&nbsp;li_clip
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_shop"></span>
		&nbsp;li_shop
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_calendar"></span>
		&nbsp;li_calendar
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_vallet"></span>
		&nbsp;li_vallet
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_vynil"></span>
		&nbsp;li_vynil
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_truck"></span>
		&nbsp;li_truck
	</span>
	<span class="box1">
		<span aria-hidden="true" class="li_world"></span>
		&nbsp;li_world
	</span>
	</section>
	<footer>
		<p>Handcrafted by <a href="http://shmidt.in">Shmidt Sergey</a></p>
		<p>Updates here: <a href="http://designmodo.com/linecons-free">http://designmodo.com/linecons-free</a></p>
	</footer>
	</div>
	<script>
	document.getElementById("glyphs").addEventListener("click", function(e) {
		var target = e.target;
		if (target.tagName === "INPUT") {
			target.select();
		}
	});
	</script>
</body>
</html>