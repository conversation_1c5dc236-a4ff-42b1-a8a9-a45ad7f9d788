<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report_detail_mcu" language="groovy" pageWidth="700" pageHeight="1008" columnWidth="660" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="d9fec2d4-a3ed-4323-9d18-6e25a12e38b1">
	<property name="ireport.zoom" value="1.6105100000000325"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<queryString>
		<![CDATA[SELECT
    CURDATE() AS TANGGAL,
    pg1.ABSEN,
    dbsdm.get_namaLengkap(pg1.ID),
    pertanyaan.pertanyaan,
    pertanyaan.id AS id_pertanyaan,
    j_kuis.id_pegawai,
    CASE
        WHEN j_kuis.jenis_pertanyaan = 6 AND j_kuis.response = 5 THEN 'Sangat Setuju'
        WHEN j_kuis.jenis_pertanyaan = 6 AND j_kuis.response = 4 THEN 'Setuju'
        WHEN j_kuis.jenis_pertanyaan = 6 AND j_kuis.response = 3 THEN 'Kurang Setuju'
        WHEN j_kuis.jenis_pertanyaan = 6 AND j_kuis.response = 2 THEN 'Tidak Setuju'
        WHEN j_kuis.jenis_pertanyaan = 6 AND j_kuis.response = 1 THEN 'Sangat Tidak Setuju'
        WHEN j_kuis.jenis_pertanyaan = 8 AND j_kuis.response = 5 THEN '5 Bintang'
        WHEN j_kuis.jenis_pertanyaan = 8 AND j_kuis.response = 4 THEN '4 Bintang'
        WHEN j_kuis.jenis_pertanyaan = 8 AND j_kuis.response = 3 THEN '3 Bintang'
        WHEN j_kuis.jenis_pertanyaan = 8 AND j_kuis.response = 2 THEN '2 Bintang'
        WHEN j_kuis.jenis_pertanyaan = 8 AND j_kuis.response = 1 THEN '1 Bintang'
        WHEN j_kuis.jenis_pertanyaan = 7 AND j_kuis.response = 1 THEN 'Seperti Saat Ini'
        WHEN j_kuis.jenis_pertanyaan = 7 AND j_kuis.response = 2 THEN 'Per Unit'
        ELSE j_kuis.response
    END AS response,
    DATE_FORMAT(COALESCE(j_kuis.created_at, NOW()), '%d/%m/%Y %H:%i:%s') AS tgl_update
FROM
    dbkuis.survey_itk j_kuis
LEFT JOIN
    dbkuis.tb_pertanyaan pertanyaan ON j_kuis.id_pertanyaan = pertanyaan.id
LEFT JOIN
    dbsdm.pegawai1 pg1 ON j_kuis.id_pegawai = pg1.ID
WHERE
    pertanyaan.jenis IN (6, 7, 8, 9)
    and pg1.ABSEN IS NOT NULL]]>
	</queryString>
	<field name="TANGGAL" class="java.sql.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ABSEN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dbsdm.get_namaLengkap(pg1.ID)" class="java.lang.String"/>
	<field name="pertanyaan" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="id_pertanyaan" class="java.lang.Integer"/>
	<field name="id_pegawai" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="response" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tgl_update" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<variable name="noUrut" class="java.lang.Integer" resetType="Group" resetGroup="nama" calculation="Count">
		<variableExpression><![CDATA[Boolean.TRUE]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="noHeader" class="java.lang.Integer" incrementType="Group" incrementGroup="nama" calculation="Count">
		<variableExpression><![CDATA[0]]></variableExpression>
		<initialValueExpression><![CDATA[+1]]></initialValueExpression>
	</variable>
	<group name="nama">
		<groupExpression><![CDATA[$F{dbsdm.get_namaLengkap(pg1.ID)}]]></groupExpression>
		<groupHeader>
			<band height="47">
				<textField>
					<reportElement x="126" y="0" width="248" height="20" uuid="cf01c25d-e564-4995-a5aa-7e2c6d895dd8"/>
					<textElement verticalAlignment="Top"/>
					<textFieldExpression><![CDATA[$F{dbsdm.get_namaLengkap(pg1.ID)}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="28" y="0" width="98" height="20" uuid="af983231-e719-411a-91d7-7595d45b0c6c"/>
					<textElement verticalAlignment="Top">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Nama Pegawai :]]></text>
				</staticText>
				<textField>
					<reportElement x="520" y="0" width="116" height="20" uuid="2a28a24d-3c19-493b-a5ac-d3e3576f844f"/>
					<textElement verticalAlignment="Top"/>
					<textFieldExpression><![CDATA[$F{tgl_update}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="387" y="0" width="133" height="20" uuid="6600d337-dd69-4019-87fd-94410f9b8959"/>
					<textElement verticalAlignment="Top">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Tanggal Kirim Data :]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="28" y="27" width="467" height="20" uuid="23eeaa9a-f41c-4b4f-99ee-a8e7c9adf4e3"/>
					<box leftPadding="2" rightPadding="2">
						<topPen lineWidth="0.75"/>
						<leftPen lineWidth="0.75"/>
						<bottomPen lineWidth="0.75"/>
						<rightPen lineWidth="0.75"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Pertanyaan]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="27" width="28" height="20" uuid="d6c976f5-426b-4ec6-8bca-faee532e4700"/>
					<box leftPadding="2" rightPadding="2">
						<topPen lineWidth="0.75"/>
						<leftPen lineWidth="0.75"/>
						<bottomPen lineWidth="0.75"/>
						<rightPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[No.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="495" y="27" width="141" height="20" uuid="6e6201a7-f9dd-447c-b6e6-9538de23e4fc"/>
					<box leftPadding="2" rightPadding="2">
						<topPen lineWidth="0.75"/>
						<leftPen lineWidth="0.75"/>
						<bottomPen lineWidth="0.75"/>
						<rightPen lineWidth="0.75"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Jawaban]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="49">
				<break>
					<reportElement x="0" y="29" width="660" height="1" uuid="4434f33e-7b88-42b7-9e92-e966efb85658"/>
				</break>
			</band>
		</groupFooter>
	</group>
	<title>
		<band height="98" splitType="Stretch">
			<staticText>
				<reportElement mode="Transparent" x="0" y="17" width="608" height="17" uuid="851a9ea7-25f0-4435-af36-29b2d3b99819"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[RUMAH SAKIT KANKER "DHARMAIS"]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="34" width="608" height="17" uuid="15ce349d-557a-4f72-be0f-cdaac388fc0a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Jl. Let. Jend S. Parman Kav. 84 - 86, Slipi, Jakarta Barat 11420]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="608" height="17" uuid="09eedea9-83fc-4d2e-ab84-d44c4ea3e5ea"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[LAPORAN SURVEY KEGIATAN INTERNALISASI TRANSFORMASI KESEHATAN MENUJU BUDAYA KERJA BARU]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy">
				<reportElement mode="Transparent" x="98" y="70" width="262" height="17" uuid="3061c967-2d3a-41db-8afb-0beefa1b2ae9"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TANGGAL}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="0" y="70" width="98" height="17" uuid="1510b300-c277-465a-b7e3-f876997e40b8"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tanggal Cetak :]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="30"/>
	</pageHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="495" y="0" width="141" height="20" uuid="03328280-e444-48ef-9e8d-bddbc396b528"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textFieldExpression><![CDATA[$F{response}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="28" height="20" uuid="b790a039-7a44-4986-8f83-6cab06d412c3"/>
				<box rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$V{noUrut}+"."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="28" y="0" width="467" height="20" uuid="4a7880d8-3f70-45d8-ad37-9c9b610c4313"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textFieldExpression><![CDATA[$F{pertanyaan}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
