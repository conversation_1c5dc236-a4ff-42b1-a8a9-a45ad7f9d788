<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="laporan_komite_etik" language="groovy" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="a02911eb-d5f1-480f-b614-25a8f8008977">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="JENIS_DOKUMEN" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT p.ABSEN
, dbsdm.get_namaLengkap(p.ID) NAMA_LENGKAP
, dd.NM_DOKUMEN NAMA_DOKUMEN
, jd.VARIABEL JENISDOKUMEN

FROM dbsdm.detail_dokumen dd

LEFT JOIN dbsdm.pegawai1 p ON dd.PEGAWAI = p.ID
LEFT JOIN dbsdm.variabel jd ON jd.ID = dd.JENIS_DOKUMEN AND jd.KATEGORI = 21

WHERE dd.JENIS_DOKUMEN = $P{JENIS_DOKUMEN}

ORDER BY p.NAMA_LENGKAP]]>
	</queryString>
	<field name="ABSEN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NAMA_LENGKAP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NAMA_DOKUMEN" class="java.lang.String"/>
	<field name="JENISDOKUMEN" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="73" splitType="Stretch">
			<staticText>
				<reportElement x="36" y="16" width="274" height="20" uuid="f5fbbeab-fbf2-4fc8-80c8-174e48db5196"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Rekap Upload Dokumen Pegawai]]></text>
			</staticText>
			<textField>
				<reportElement x="36" y="36" width="766" height="20" uuid="70254dae-d477-4b1b-bdb0-4e1f124ff094"/>
				<textElement verticalAlignment="Middle">
					<font size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{JENISDOKUMEN}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="36" height="20" uuid="64943516-c7a2-490a-b6e1-1a259a854f42"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[No]]></text>
			</staticText>
			<staticText>
				<reportElement x="122" y="0" width="340" height="20" uuid="1163c373-4dc2-4cb1-bdd8-b37b0648dceb"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Nama Pegawai]]></text>
			</staticText>
			<staticText>
				<reportElement x="462" y="0" width="340" height="20" uuid="b0f3e7cd-f379-4bcb-9bb5-734f302074ad"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Nama Dokumen]]></text>
			</staticText>
			<staticText>
				<reportElement x="36" y="0" width="86" height="20" uuid="7eaf9c04-d244-4f3e-942c-9777a5893492"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Absen]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="36" height="20" isPrintWhenDetailOverflows="true" uuid="914ebdec-a651-44e5-97ba-175ebff6747b"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="122" y="0" width="340" height="20" isPrintWhenDetailOverflows="true" uuid="08a3687f-e3db-4ddb-b174-dd783eeb9495"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NAMA_LENGKAP}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="462" y="0" width="340" height="20" isPrintWhenDetailOverflows="true" uuid="0236196d-f33a-4b1b-80fa-3cd43a5c8b1e"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NAMA_DOKUMEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="36" y="0" width="86" height="20" isPrintWhenDetailOverflows="true" uuid="051eb150-a3d6-4db8-b2b3-6be043f45deb"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ABSEN}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="45" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="54" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
