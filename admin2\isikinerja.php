<?php
	include '../proses/dbkoneksi.php';
	$sts = 1;
	$oleh = 55555;
	// $pegawai = 3221;
	// $pegawai = 1747;

	// $pegawaiC = 1672;
	$query2 = "SELECT * FROM kuantitas WHERE PEGAWAI = '$pegawai' AND STATUS = 1 ";
	$rs2 = mysqli_query($conn,$query2);
	if (mysqli_num_rows($rs2)>0) {
		// echo "Ada<br>";
		$query3 = "UPDATE kuantitas SET STATUS = 2 WHERE PEGAWAI = '$pegawai' AND STATUS = 1 ";
		// echo $query3."<br>";
		$rs3 = mysqli_query($conn,$query3);
		if ($rs3) {
			$query = "SELECT INDIKATOR, DO, TARGET, BOBOT FROM kuantitas WHERE PEGAWAI = '$pegawaiC' AND STATUS = 1";
			$rs = mysqli_query($conn,$query);
			while ($data = mysqli_fetch_array($rs)) {
				// echo "Indikator = ".$data[0]." DO = ".$data[1]." TARGET = ".$data[2]." BOBOT = ".$data[3]."<br><br>";
				$query1 = "INSERT INTO kuantitas VALUES('','$pegawai','$data[0]','$data[1]','$data[2]','$data[3]','$sts','$oleh',NOW())";
				// echo $query1."<br><br>";
				$rs1 = mysqli_query($conn,$query1);
				if ($rs1) {
					echo "Sukses Upload<br>";
				}
			}
		}else{
			echo "Gagal 1 <br>";
		}
		
	}else{
		$query = "SELECT INDIKATOR, DO, TARGET, BOBOT FROM kuantitas WHERE PEGAWAI = '$pegawaiC' AND STATUS = 1";
		$rs = mysqli_query($conn,$query);
		while ($data = mysqli_fetch_array($rs)) {
			// echo "Indikator = ".$data[0]." DO = ".$data[1]." TARGET = ".$data[2]." BOBOT = ".$data[3]."<br><br>";
			$query1 = "INSERT INTO kuantitas VALUES('','$pegawai','$data[0]','$data[1]','$data[2]','$data[3]','$sts','$oleh',NOW())";
			// echo $query1."<br><br>";
			$rs1 = mysqli_query($conn,$query1);
			if ($rs1) {
				echo "Sukses Upload<br>";
			}
		}
	}
	// $indeex = 1;
	// while ($dataan = mysqli_fetch_array($rs2)) {
	// 	echo $indeex."<br>";
	// 	$indeex++;
	// }
	
?>